# Extension Assets

## Icon Requirements

The extension needs a 128x128 PNG icon. 

To create the icon:
1. Use the provided `icon.svg` file
2. Convert it to PNG format at 128x128 resolution
3. Save as `icon.png` in this directory

You can use online converters like:
- https://svgtopng.com/
- https://www.freeconvert.com/svg-to-png
- https://picflow.com/convert/svg-to-png

Or use command line tools like ImageMagick:
```bash
magick icon.svg -resize 128x128 icon.png
```
