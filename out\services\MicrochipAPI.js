"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.microchipAPI = exports.MicrochipAPI = void 0;
const LangChainAgentService_1 = require("./LangChainAgentService");
const AdvancedAgentTools_1 = require("./AdvancedAgentTools");
const VSCodeContextService_1 = require("./VSCodeContextService");
const VectorStoreService_1 = require("./VectorStoreService");
const https = __importStar(require("https"));
const http = __importStar(require("http"));
class MicrochipAPI {
    constructor() {
        this.apiKey = '';
        this.conversationHistory = [];
        this.baseURL = 'http://localhost:3001';
        this.endpoint = '/api/chat';
        this.agentMode = false;
        this.advancedAgentMode = false;
        this.availableTools = new Map();
        // Advanced agent services
        this.langChainAgent = null;
        this.advancedTools = null;
        this.vscodeContext = null;
        this.vectorStore = null;
        this.openaiApiKey = '';
        this.ideOrchestrator = null; // AdvancedIDEOrchestrator
        this.conversationContext = {
            history: [],
            userPreferences: {},
            sessionData: {}
        };
        this.initializeTools();
        this.initializeAdvancedServices();
    }
    makeHttpRequest(url, options) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const httpModule = isHttps ? https : http;
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            };
            const req = httpModule.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    const response = {
                        ok: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        json: () => Promise.resolve(JSON.parse(data)),
                        text: () => Promise.resolve(data)
                    };
                    resolve(response);
                });
            });
            req.on('error', (error) => {
                reject(error);
            });
            if (options.signal) {
                options.signal.addEventListener('abort', () => {
                    req.destroy();
                    reject(new Error('Request aborted'));
                });
            }
            if (options.body) {
                req.write(options.body);
            }
            req.end();
        });
    }
    setApiKey(apiKey) {
        this.apiKey = apiKey.trim();
    }
    setOpenAIApiKey(apiKey) {
        this.openaiApiKey = apiKey.trim();
        this.initializeAdvancedServices();
    }
    setAgentMode(enabled) {
        this.agentMode = enabled;
    }
    setAdvancedAgentMode(enabled) {
        this.advancedAgentMode = enabled;
        if (enabled && !this.langChainAgent) {
            this.initializeAdvancedServices();
        }
    }
    getAgentMode() {
        return this.agentMode;
    }
    getAdvancedAgentMode() {
        return this.advancedAgentMode;
    }
    getAvailableTools() {
        return Array.from(this.availableTools.values());
    }
    getConversationContext() {
        return this.conversationContext;
    }
    // Test method to verify agent functionality
    testAgentClassification(query) {
        return this.classifyIntent(query);
    }
    getApiKey() {
        return this.apiKey;
    }
    clearHistory() {
        this.conversationHistory = [];
        this.conversationContext.history = [];
        this.conversationContext.sessionData = {};
        if (this.langChainAgent) {
            this.langChainAgent.clearMemory();
        }
    }
    async initializeAdvancedServices() {
        try {
            if (!this.openaiApiKey) {
                console.warn('OpenAI API key not set, advanced agent features will be limited');
                return;
            }
            // Get workspace root
            const workspaceRoot = this.conversationContext.workspaceRoot || process.cwd();
            // Initialize services
            this.advancedTools = new AdvancedAgentTools_1.AdvancedAgentTools(workspaceRoot);
            this.vscodeContext = new VSCodeContextService_1.VSCodeContextService();
            this.vectorStore = new VectorStoreService_1.VectorStoreService(workspaceRoot, this.openaiApiKey);
            // Initialize IDE Orchestrator
            const { AdvancedIDEOrchestrator } = await Promise.resolve().then(() => __importStar(require('./AdvancedIDEOrchestrator')));
            this.ideOrchestrator = new AdvancedIDEOrchestrator();
            await this.ideOrchestrator.initialize();
            // Initialize LangChain agent
            this.langChainAgent = new LangChainAgentService_1.LangChainAgentService(this.openaiApiKey, workspaceRoot, this.conversationContext);
            await this.langChainAgent.initialize();
            // Register additional IDE-specific tools
            this.registerIDETools();
            console.log('Advanced agent services initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize advanced agent services:', error);
        }
    }
    registerIDETools() {
        if (!this.advancedTools || !this.ideOrchestrator)
            return;
        // Add IDE command execution tool
        const ideCommandTool = {
            name: 'ide_command',
            description: 'Execute IDE commands like project analysis, code generation, refactoring, etc.',
            keywords: ['ide', 'analyze', 'generate', 'refactor', 'optimize', 'security', 'performance'],
            execute: async (query, _context) => {
                try {
                    // Parse IDE command from query
                    const commandMap = {
                        'analyze project': 'ide.analyzeProject',
                        'analyze code': 'ide.analyzeProject',
                        'generate code': 'ide.generateCode',
                        'refactor code': 'ide.refactorCode',
                        'detect errors': 'ide.detectErrors',
                        'security scan': 'ide.securityScan',
                        'performance analysis': 'ide.performanceAnalysis',
                        'optimize project': 'ide.optimizeProject',
                        'setup ci': 'ide.setupCI',
                        'generate report': 'ide.generateReport'
                    };
                    let commandId = '';
                    let args = undefined;
                    // Find matching command
                    for (const [keyword, id] of Object.entries(commandMap)) {
                        if (query.toLowerCase().includes(keyword)) {
                            commandId = id;
                            break;
                        }
                    }
                    if (!commandId) {
                        return 'I can help with: project analysis, code generation, refactoring, error detection, security scanning, performance analysis, project optimization, CI setup, and diagnostic reports. What would you like me to do?';
                    }
                    // Special handling for code generation
                    if (commandId === 'ide.generateCode') {
                        const componentMatch = query.match(/(?:component|class|service|utility)\s+(\w+)/i);
                        if (componentMatch) {
                            args = {
                                type: 'component',
                                name: componentMatch[1],
                                description: `Generated ${componentMatch[1]} based on user request`,
                                options: { withProps: true, withStyles: true, withTests: true },
                                context: {
                                    projectType: 'application',
                                    framework: 'react',
                                    language: 'typescript',
                                    conventions: { naming: 'camelCase', indentation: 'spaces', quotes: 'single' },
                                    existingPatterns: [],
                                    dependencies: []
                                }
                            };
                        }
                    }
                    const result = await this.ideOrchestrator.executeCommand(commandId, args);
                    // Format result for display
                    if (typeof result === 'object') {
                        return this.formatIDEResult(commandId, result);
                    }
                    return result || 'Command executed successfully';
                }
                catch (error) {
                    return `IDE command failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
        this.availableTools.set('ide_command', ideCommandTool);
    }
    formatIDEResult(commandId, result) {
        try {
            const safeResult = result; // Using any for flexibility with dynamic result structures
            switch (commandId) {
                case 'ide.analyzeProject':
                    return `📊 Project Analysis Results:
- Complexity: ${safeResult?.metrics?.complexity?.toFixed(2) || 'N/A'}
- Maintainability: ${safeResult?.metrics?.maintainability?.toFixed(2) || 'N/A'}
- Code Smells: ${safeResult?.metrics?.codeSmells?.length || 0}
- Security Issues: ${safeResult?.securityAnalysis?.vulnerabilities?.length || 0}
- Recommendations: ${safeResult?.recommendations?.length || 0} suggestions`;
                case 'ide.generateCode':
                    return `✨ Code Generation Complete:
- Generated ${safeResult?.files?.length || 0} files
- Dependencies: ${safeResult?.dependencies?.join(', ') || 'None'}
- Instructions: ${safeResult?.instructions?.length || 0} steps`;
                case 'ide.generateReport':
                    return `📋 Diagnostic Report:
- Total Errors: ${safeResult?.summary?.totalErrors || 0}
- Critical Errors: ${safeResult?.summary?.criticalErrors || 0}
- Fixable Errors: ${safeResult?.summary?.fixableErrors || 0}
- Estimated Fix Time: ${safeResult?.summary?.estimatedFixTime || 0} minutes
- Recommendations: ${safeResult?.recommendations?.length || 0}`;
                case 'ide.optimizeProject':
                    return `🚀 Project Optimization Complete:
${Array.isArray(result) ? result.map(item => `- ${item}`).join('\n') : 'Optimizations applied'}`;
                default:
                    return JSON.stringify(result, null, 2);
            }
        }
        catch (error) {
            return `Result formatting error: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
    }
    async indexWorkspace() {
        if (this.langChainAgent) {
            await this.langChainAgent.indexWorkspace();
        }
    }
    initializeTools() {
        // Microchip API Tool
        this.availableTools.set('microchip-api', {
            name: 'Microchip API',
            description: 'Direct access to Microchip AI API for general questions about microcontrollers, development tools, and products',
            keywords: ['microcontroller', 'pic', 'avr', 'sam', 'mplab', 'atmel', 'development', 'programming', 'code', 'hardware', 'datasheet', 'peripheral'],
            execute: async (query, _context) => {
                return await this.callAPI(query);
            }
        });
        // Code Analysis Tool
        this.availableTools.set('code-analysis', {
            name: 'Code Analysis',
            description: 'Analyze code snippets, debug issues, and provide optimization suggestions',
            keywords: ['debug', 'error', 'compile', 'optimization', 'review', 'analyze', 'fix', 'bug', 'syntax', 'performance'],
            execute: async (query, _context) => {
                const enhancedQuery = `Please analyze this code or help with this programming issue: ${query}. Provide detailed analysis, identify potential issues, and suggest improvements.`;
                return await this.callAPI(enhancedQuery);
            }
        });
        // Documentation Tool
        this.availableTools.set('documentation', {
            name: 'Documentation Assistant',
            description: 'Help find and explain documentation, datasheets, and technical specifications',
            keywords: ['documentation', 'datasheet', 'manual', 'reference', 'specification', 'guide', 'tutorial', 'example', 'api', 'register'],
            execute: async (query, _context) => {
                const enhancedQuery = `Help me find or understand documentation: ${query}. Please provide detailed explanations and point me to relevant resources.`;
                return await this.callAPI(enhancedQuery);
            }
        });
        // Project Setup Tool
        this.availableTools.set('project-setup', {
            name: 'Project Setup Assistant',
            description: 'Help with project configuration, toolchain setup, and development environment',
            keywords: ['setup', 'configure', 'install', 'toolchain', 'environment', 'project', 'makefile', 'build', 'compiler', 'linker'],
            execute: async (query, _context) => {
                const enhancedQuery = `Help me with project setup or configuration: ${query}. Please provide step-by-step instructions and best practices.`;
                return await this.callAPI(enhancedQuery);
            }
        });
        // Troubleshooting Tool
        this.availableTools.set('troubleshooting', {
            name: 'Troubleshooting Assistant',
            description: 'Diagnose and solve hardware and software issues',
            keywords: ['problem', 'issue', 'not working', 'failed', 'error', 'troubleshoot', 'diagnose', 'solve', 'broken', 'malfunction'],
            execute: async (query, _context) => {
                const enhancedQuery = `Help me troubleshoot this issue: ${query}. Please provide systematic diagnostic steps and potential solutions.`;
                return await this.callAPI(enhancedQuery);
            }
        });
    }
    async testConnection() {
        if (!this.apiKey) {
            return {
                success: false,
                message: '',
                error: 'API key is required'
            };
        }
        try {
            const response = await this.callAPI('Hello, are you working?');
            return {
                success: true,
                message: response
            };
        }
        catch (error) {
            return {
                success: false,
                message: '',
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    async sendMessage(userMessage) {
        if (!this.apiKey) {
            throw new Error('API key is required');
        }
        if (!userMessage.trim()) {
            throw new Error('Message cannot be empty');
        }
        // Add user message to conversation context
        const userChatMessage = {
            id: Date.now().toString(),
            text: userMessage,
            isBot: false,
            timestamp: new Date()
        };
        this.conversationContext.history.push(userChatMessage);
        let response;
        let intent;
        let toolUsed;
        let confidence;
        if (this.advancedAgentMode && this.langChainAgent) {
            // Use advanced LangChain agent
            const agentResponse = await this.langChainAgent.processWithAdvancedAgent(userMessage);
            response = agentResponse.message;
            intent = agentResponse.intent;
            toolUsed = agentResponse.toolUsed;
            confidence = agentResponse.confidence;
        }
        else if (this.agentMode) {
            // Use basic agent mode with intent detection and tool routing
            const agentResponse = await this.processWithAgent(userMessage);
            response = agentResponse.message;
            intent = agentResponse.intent;
            toolUsed = agentResponse.toolUsed;
            confidence = agentResponse.confidence;
        }
        else {
            // Use direct API call
            response = await this.callDirectAPI(userMessage);
        }
        // Add bot response to conversation context
        const botChatMessage = {
            id: (Date.now() + 1).toString(),
            text: response,
            isBot: true,
            timestamp: new Date(),
            intent,
            toolUsed,
            confidence
        };
        this.conversationContext.history.push(botChatMessage);
        // Update legacy conversation history for backward compatibility
        this.conversationHistory.push(userMessage);
        // Keep only last 5 messages for context
        if (this.conversationHistory.length > 5) {
            this.conversationHistory = this.conversationHistory.slice(-5);
        }
        // Keep conversation context history manageable
        if (this.conversationContext.history.length > 20) {
            this.conversationContext.history = this.conversationContext.history.slice(-20);
        }
        return {
            success: true,
            message: response,
            intent,
            toolUsed,
            confidence
        };
    }
    async processWithAgent(userMessage) {
        // Classify intent
        const classification = this.classifyIntent(userMessage);
        // Get the appropriate tool
        const tool = this.availableTools.get(classification.suggestedTool);
        if (!tool) {
            // Fallback to direct API if no tool found
            const response = await this.callDirectAPI(userMessage);
            return {
                success: true,
                message: response,
                intent: classification.intent,
                toolUsed: 'microchip-api',
                confidence: classification.confidence
            };
        }
        // Execute the tool
        const response = await tool.execute(userMessage, this.conversationContext);
        return {
            success: true,
            message: response,
            intent: classification.intent,
            toolUsed: tool.name,
            confidence: classification.confidence
        };
    }
    classifyIntent(userMessage) {
        const message = userMessage.toLowerCase();
        const words = message.split(/\s+/);
        let bestMatch = {
            tool: 'microchip-api',
            score: 0,
            intent: 'general-inquiry'
        };
        // Score each tool based on keyword matches
        for (const [toolName, tool] of this.availableTools) {
            let score = 0;
            for (const keyword of tool.keywords) {
                if (message.includes(keyword.toLowerCase())) {
                    score += 2; // Exact keyword match
                }
                // Check for partial matches
                for (const word of words) {
                    if (word.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(word)) {
                        score += 1;
                    }
                }
            }
            if (score > bestMatch.score) {
                bestMatch = {
                    tool: toolName,
                    score,
                    intent: this.getIntentFromTool(toolName)
                };
            }
        }
        // Calculate confidence based on score
        const maxPossibleScore = Math.max(...Array.from(this.availableTools.values()).map(t => t.keywords.length * 2));
        const confidence = Math.min(bestMatch.score / maxPossibleScore, 1.0);
        return {
            intent: bestMatch.intent,
            confidence,
            suggestedTool: bestMatch.tool,
            reasoning: `Matched ${bestMatch.score} keywords for ${bestMatch.tool}`
        };
    }
    getIntentFromTool(toolName) {
        const intentMap = {
            'microchip-api': 'general-inquiry',
            'code-analysis': 'code-help',
            'documentation': 'documentation-request',
            'project-setup': 'setup-assistance',
            'troubleshooting': 'problem-solving'
        };
        return intentMap[toolName] || 'general-inquiry';
    }
    async callDirectAPI(userMessage) {
        return await this.callAPI(userMessage);
    }
    async callAPI(userMessage) {
        const requestBody = {
            questions: [userMessage],
            answers: this.conversationHistory.slice(-5),
            category: 101,
            logQnA: true,
            client: "react-chatbot",
            apiKey: this.apiKey
        };
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
        try {
            // Use Node.js compatible HTTP request
            const response = await this.makeHttpRequest(`${this.baseURL}${this.endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            const data = await response.json();
            if (!response.ok) {
                if (data.error) {
                    throw new Error(data.error);
                }
                else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
            if (data.success && data.message) {
                return data.message;
            }
            else if (data.error) {
                throw new Error(data.error);
            }
            else {
                throw new Error('Unexpected response format from server');
            }
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error('Request timeout - Server took too long to respond');
                }
                else if (error.message.includes('Failed to fetch')) {
                    throw new Error('Network error - Please check if the backend server is running');
                }
                else {
                    throw error;
                }
            }
            else {
                throw new Error('An unexpected error occurred');
            }
        }
    }
}
exports.MicrochipAPI = MicrochipAPI;
// Create a singleton instance
exports.microchipAPI = new MicrochipAPI();
//# sourceMappingURL=MicrochipAPI.js.map