"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserInteractionIntelligence = void 0;
const vscode = __importStar(require("vscode"));
class UserInteractionIntelligence {
    constructor() {
        this.context = this.initializeContext();
        this.conversationMemory = this.initializeConversationMemory();
        this.userProfile = this.loadUserProfile();
        this.intentClassifier = new IntentClassifier();
        this.suggestionEngine = new ProactiveSuggestionEngine(this.userProfile);
        this.adaptationEngine = new AdaptationEngine();
        this.setupContextTracking();
    }
    // Analyze user input and determine intent
    async analyzeUserInput(input) {
        const intent = await this.intentClassifier.classify(input, this.context);
        // Update conversation memory
        this.conversationMemory.messages.push({
            id: Date.now().toString(),
            role: 'user',
            content: input,
            timestamp: new Date(),
            intent
        });
        // Learn from user patterns
        await this.adaptationEngine.learnFromInteraction(intent, this.context);
        return intent;
    }
    // Generate contextual response
    async generateResponse(intent, input) {
        let response = '';
        switch (intent.category) {
            case 'code':
                response = await this.handleCodeRequest(intent, input);
                break;
            case 'debug':
                response = await this.handleDebugRequest(intent, input);
                break;
            case 'refactor':
                response = await this.handleRefactorRequest(intent, input);
                break;
            case 'generate':
                response = await this.handleGenerateRequest(intent, input);
                break;
            case 'explain':
                response = await this.handleExplainRequest(intent, input);
                break;
            case 'search':
                response = await this.handleSearchRequest(intent, input);
                break;
            default:
                response = await this.handleGeneralRequest(intent, input);
        }
        // Adapt response to user preferences
        response = this.adaptationEngine.adaptResponse(response, this.userProfile);
        // Store assistant response
        this.conversationMemory.messages.push({
            id: Date.now().toString(),
            role: 'assistant',
            content: response,
            timestamp: new Date()
        });
        return response;
    }
    // Get proactive suggestions
    async getProactiveSuggestions() {
        return this.suggestionEngine.generateSuggestions(this.context, this.conversationMemory);
    }
    // Update user preferences
    updateUserPreferences(preferences) {
        this.context.userPreferences = { ...this.context.userPreferences, ...preferences };
        this.saveUserProfile();
    }
    // Track user feedback
    async trackFeedback(messageId, feedback) {
        const message = this.conversationMemory.messages.find(m => m.id === messageId);
        if (message) {
            message.metadata = { ...message.metadata, feedback };
            await this.adaptationEngine.learnFromFeedback(message, feedback);
        }
    }
    // Initialize context tracking
    setupContextTracking() {
        // Track file changes
        vscode.window.onDidChangeActiveTextEditor((editor) => {
            if (editor) {
                this.context.currentFile = editor.document.fileName;
                this.context.recentActions.push({
                    type: 'file-open',
                    timestamp: new Date(),
                    details: { file: editor.document.fileName }
                });
            }
        });
        // Track text selection
        vscode.window.onDidChangeTextEditorSelection((event) => {
            const selection = event.textEditor.selection;
            if (!selection.isEmpty) {
                this.context.selectedText = event.textEditor.document.getText(selection);
                this.context.cursorPosition = {
                    line: selection.start.line,
                    character: selection.start.character
                };
                this.context.recentActions.push({
                    type: 'text-select',
                    timestamp: new Date(),
                    details: { text: this.context.selectedText }
                });
            }
        });
    }
    // Request handlers
    async handleCodeRequest(intent, input) {
        const codeEntity = intent.entities.find(e => e.type === 'function' || e.type === 'variable');
        if (codeEntity) {
            return `I can help you with ${codeEntity.value}. Let me analyze the code and provide assistance.`;
        }
        return "I'm ready to help with your code. Could you provide more specific details about what you need?";
    }
    async handleDebugRequest(intent, input) {
        if (this.context.selectedText) {
            return `I see you've selected some code. Let me help you debug this section. Common issues to check: variable initialization, null checks, and logic flow.`;
        }
        return "I can help you debug your code. Please share the problematic code or describe the issue you're experiencing.";
    }
    async handleRefactorRequest(intent, input) {
        return "I can suggest refactoring improvements. Let me analyze your code for opportunities to improve structure, performance, and maintainability.";
    }
    async handleGenerateRequest(intent, input) {
        const techEntity = intent.entities.find(e => e.type === 'technology');
        if (techEntity) {
            return `I can generate ${techEntity.value} code for you. What specific functionality do you need?`;
        }
        return "I can generate code based on your requirements. Please describe what you'd like me to create.";
    }
    async handleExplainRequest(intent, input) {
        const conceptEntity = intent.entities.find(e => e.type === 'concept');
        if (conceptEntity) {
            const level = this.userProfile.skillLevel;
            return `Let me explain ${conceptEntity.value} at a ${level} level...`;
        }
        return "I'd be happy to explain any concept or code. What would you like me to clarify?";
    }
    async handleSearchRequest(intent, input) {
        return "I can help you search through your codebase. What are you looking for?";
    }
    async handleGeneralRequest(intent, input) {
        return "I'm here to help with your development needs. How can I assist you today?";
    }
    // Initialization methods
    initializeContext() {
        return {
            recentActions: [],
            workspaceState: {
                openFiles: [],
                lastModified: new Date(),
                projectType: 'unknown'
            },
            userPreferences: {
                codeStyle: 'concise',
                explanationLevel: 'intermediate',
                preferredLanguages: ['typescript', 'javascript'],
                autoSuggestions: true,
                proactiveHelp: true
            }
        };
    }
    initializeConversationMemory() {
        return {
            sessionId: Date.now().toString(),
            messages: [],
            context: {},
            userProfile: this.userProfile,
            startTime: new Date(),
            lastActivity: new Date()
        };
    }
    loadUserProfile() {
        // In a real implementation, this would load from persistent storage
        return {
            skillLevel: 'intermediate',
            explanationLevel: 'intermediate',
            preferredTopics: ['typescript', 'react', 'nodejs'],
            learningGoals: ['improve code quality', 'learn best practices'],
            commonPatterns: [],
            helpfulFeatures: []
        };
    }
    saveUserProfile() {
        // In a real implementation, this would save to persistent storage
    }
}
exports.UserInteractionIntelligence = UserInteractionIntelligence;
// Intent Classification Engine
class IntentClassifier {
    async classify(input, context) {
        const lowercaseInput = input.toLowerCase();
        // Simple rule-based classification (would be ML-based in production)
        let category = 'help';
        let type = 'question';
        if (lowercaseInput.includes('debug') || lowercaseInput.includes('error') || lowercaseInput.includes('fix')) {
            category = 'debug';
            type = 'request';
        }
        else if (lowercaseInput.includes('refactor') || lowercaseInput.includes('improve') || lowercaseInput.includes('optimize')) {
            category = 'refactor';
            type = 'request';
        }
        else if (lowercaseInput.includes('generate') || lowercaseInput.includes('create') || lowercaseInput.includes('make')) {
            category = 'generate';
            type = 'command';
        }
        else if (lowercaseInput.includes('explain') || lowercaseInput.includes('what is') || lowercaseInput.includes('how does')) {
            category = 'explain';
            type = 'question';
        }
        else if (lowercaseInput.includes('search') || lowercaseInput.includes('find')) {
            category = 'search';
            type = 'command';
        }
        return {
            type,
            category,
            confidence: 0.8,
            entities: this.extractEntities(input),
            context
        };
    }
    extractEntities(input) {
        const entities = [];
        // Simple entity extraction (would be more sophisticated in production)
        const words = input.split(/\s+/);
        words.forEach((word, index) => {
            if (word.endsWith('.ts') || word.endsWith('.js') || word.endsWith('.tsx') || word.endsWith('.jsx')) {
                entities.push({
                    type: 'file',
                    value: word,
                    confidence: 0.9
                });
            }
        });
        return entities;
    }
}
// Proactive Suggestion Engine
class ProactiveSuggestionEngine {
    constructor(userProfile) {
        this.userProfile = userProfile;
    }
    generateSuggestions(context, memory) {
        const suggestions = [];
        // Suggest based on current context
        if (context.currentFile?.endsWith('.ts') && context.selectedText) {
            suggestions.push({
                id: 'type-check',
                type: 'tip',
                title: 'Type Check Selected Code',
                description: 'Run TypeScript type checking on the selected code',
                priority: 'medium',
                relevanceScore: 0.8,
                dismissible: true
            });
        }
        return suggestions;
    }
}
// Adaptation Engine
class AdaptationEngine {
    async learnFromInteraction(intent, context) {
        // Learn from user interaction patterns
    }
    async learnFromFeedback(message, feedback) {
        // Learn from user feedback
    }
    adaptResponse(response, userProfile) {
        // Adapt response based on user preferences
        if (userProfile.explanationLevel === 'beginner') {
            return response + '\n\n💡 Tip: This is a common pattern in software development.';
        }
        return response;
    }
}
//# sourceMappingURL=UserInteractionIntelligence.js.map