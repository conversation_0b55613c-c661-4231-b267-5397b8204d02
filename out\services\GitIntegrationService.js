"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitIntegrationService = void 0;
const vscode = __importStar(require("vscode"));
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class GitIntegrationService {
    constructor(workspaceRoot) {
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    }
    async getStatus() {
        try {
            const { stdout } = await execAsync('git status --porcelain -b', { cwd: this.workspaceRoot });
            return this.parseStatus(stdout);
        }
        catch (error) {
            throw new Error(`Failed to get git status: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    parseStatus(output) {
        const lines = output.split('\n').filter(line => line.trim());
        const branchLine = lines.find(line => line.startsWith('##'));
        let branch = 'main';
        let ahead = 0;
        let behind = 0;
        if (branchLine) {
            const branchMatch = branchLine.match(/## ([^.]+)/);
            if (branchMatch)
                branch = branchMatch[1];
            const aheadMatch = branchLine.match(/ahead (\d+)/);
            const behindMatch = branchLine.match(/behind (\d+)/);
            if (aheadMatch)
                ahead = parseInt(aheadMatch[1]);
            if (behindMatch)
                behind = parseInt(behindMatch[1]);
        }
        const staged = [];
        const unstaged = [];
        const untracked = [];
        const conflicted = [];
        lines.forEach(line => {
            if (line.startsWith('##'))
                return;
            const statusCode = line.substring(0, 2);
            const filePath = line.substring(3);
            const file = {
                path: filePath,
                status: this.getFileStatus(statusCode)
            };
            if (statusCode.includes('U') || statusCode.includes('A') && statusCode.includes('A')) {
                conflicted.push(file);
            }
            else if (statusCode[0] !== ' ') {
                staged.push(file);
            }
            else if (statusCode[1] !== ' ') {
                unstaged.push(file);
            }
            else if (statusCode === '??') {
                untracked.push(file);
            }
        });
        return { branch, ahead, behind, staged, unstaged, untracked, conflicted };
    }
    getFileStatus(statusCode) {
        const code = statusCode.trim();
        switch (code[0] || code[1]) {
            case 'A': return 'added';
            case 'M': return 'modified';
            case 'D': return 'deleted';
            case 'R': return 'renamed';
            case 'C': return 'copied';
            case '?': return 'untracked';
            case 'U': return 'conflicted';
            default: return 'modified';
        }
    }
    async addFiles(files) {
        try {
            const fileArgs = files.length === 0 ? '.' : files.map(f => `"${f}"`).join(' ');
            const { stdout, stderr } = await execAsync(`git add ${fileArgs}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async commit(message, files) {
        try {
            // Add files if specified
            if (files && files.length > 0) {
                await this.addFiles(files);
            }
            const { stdout, stderr } = await execAsync(`git commit -m "${message}"`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async push(remote = 'origin', branch) {
        try {
            const branchArg = branch ? ` ${branch}` : '';
            const { stdout, stderr } = await execAsync(`git push ${remote}${branchArg}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async pull(remote = 'origin', branch) {
        try {
            const branchArg = branch ? ` ${branch}` : '';
            const { stdout, stderr } = await execAsync(`git pull ${remote}${branchArg}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async createBranch(branchName, checkout = true) {
        try {
            const checkoutFlag = checkout ? '-b' : '';
            const { stdout, stderr } = await execAsync(`git ${checkout ? 'checkout' : 'branch'} ${checkoutFlag} ${branchName}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async switchBranch(branchName) {
        try {
            const { stdout, stderr } = await execAsync(`git checkout ${branchName}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async getBranches() {
        try {
            const { stdout } = await execAsync('git branch -vv', { cwd: this.workspaceRoot });
            return this.parseBranches(stdout);
        }
        catch (error) {
            throw new Error(`Failed to get branches: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    parseBranches(output) {
        return output.split('\n')
            .filter(line => line.trim())
            .map(line => {
            const current = line.startsWith('*');
            const cleanLine = line.replace(/^\*?\s+/, '');
            const parts = cleanLine.split(/\s+/);
            const name = parts[0];
            return {
                name,
                current,
                remote: parts[2]?.includes('[') ? parts[2].replace(/[\[\]]/g, '') : undefined
            };
        });
    }
    async getCommitHistory(limit = 10) {
        try {
            const { stdout } = await execAsync(`git log --oneline -${limit} --pretty=format:"%H|%an|%ad|%s" --date=iso`, { cwd: this.workspaceRoot });
            return this.parseCommits(stdout);
        }
        catch (error) {
            throw new Error(`Failed to get commit history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    parseCommits(output) {
        return output.split('\n')
            .filter(line => line.trim())
            .map(line => {
            const [hash, author, date, message] = line.split('|');
            return {
                hash,
                author,
                date: new Date(date),
                message,
                files: [] // Would need separate call to get files
            };
        });
    }
    async getRemotes() {
        try {
            const { stdout } = await execAsync('git remote -v', { cwd: this.workspaceRoot });
            return this.parseRemotes(stdout);
        }
        catch (error) {
            throw new Error(`Failed to get remotes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    parseRemotes(output) {
        const remotes = [];
        output.split('\n')
            .filter(line => line.trim())
            .forEach(line => {
            const parts = line.split(/\s+/);
            if (parts.length >= 3) {
                remotes.push({
                    name: parts[0],
                    url: parts[1],
                    type: parts[2].includes('fetch') ? 'fetch' : 'push'
                });
            }
        });
        return remotes;
    }
    async stash(message) {
        try {
            const messageArg = message ? ` -m "${message}"` : '';
            const { stdout, stderr } = await execAsync(`git stash${messageArg}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async stashPop() {
        try {
            const { stdout, stderr } = await execAsync('git stash pop', { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async resetFile(filePath) {
        try {
            const { stdout, stderr } = await execAsync(`git checkout -- "${filePath}"`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async getDiff(filePath) {
        try {
            const fileArg = filePath ? ` "${filePath}"` : '';
            const { stdout } = await execAsync(`git diff${fileArg}`, { cwd: this.workspaceRoot });
            return stdout;
        }
        catch (error) {
            throw new Error(`Failed to get diff: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async isGitRepository() {
        try {
            await execAsync('git rev-parse --git-dir', { cwd: this.workspaceRoot });
            return true;
        }
        catch {
            return false;
        }
    }
    async initRepository() {
        try {
            const { stdout, stderr } = await execAsync('git init', { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async addRemote(name, url) {
        try {
            const { stdout, stderr } = await execAsync(`git remote add ${name} ${url}`, { cwd: this.workspaceRoot });
            return { success: true, output: stdout + (stderr || '') };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}
exports.GitIntegrationService = GitIntegrationService;
//# sourceMappingURL=GitIntegrationService.js.map