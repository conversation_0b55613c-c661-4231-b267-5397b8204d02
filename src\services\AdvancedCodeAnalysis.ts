import * as fs from 'fs-extra';
import * as path from 'path';
import { glob } from 'glob';
import * as vscode from 'vscode';

// Advanced Code Analysis Interfaces
export interface CodeMetrics {
  complexity: number;
  maintainability: number;
  testCoverage: number;
  codeSmells: CodeSmell[];
  dependencies: DependencyAnalysis;
  performance: PerformanceMetrics;
}

export interface CodeSmell {
  type: 'duplication' | 'complexity' | 'naming' | 'structure' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  file: string;
  line: number;
  description: string;
  suggestion: string;
}

export interface DependencyAnalysis {
  internal: string[];
  external: string[];
  circular: string[];
  unused: string[];
  outdated: string[];
}

export interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  memoryUsage: number;
  potentialOptimizations: string[];
}

export interface RefactoringOpportunity {
  type: 'extract-function' | 'extract-class' | 'inline' | 'rename' | 'move';
  file: string;
  startLine: number;
  endLine: number;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
}

export interface SecurityAnalysis {
  vulnerabilities: SecurityVulnerability[];
  riskScore: number;
  recommendations: string[];
}

export interface SecurityVulnerability {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  file: string;
  line: number;
  description: string;
  cwe?: string;
}

export class AdvancedCodeAnalysis {
  private workspaceRoot: string;
  private analysisCache: Map<string, any> = new Map();

  constructor(workspaceRoot?: string) {
    this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
  }

  // Comprehensive project analysis
  async analyzeProject(): Promise<{
    metrics: CodeMetrics;
    refactoringOpportunities: RefactoringOpportunity[];
    securityAnalysis: SecurityAnalysis;
    recommendations: string[];
  }> {
    const files = await this.getProjectFiles();
    
    const metrics = await this.calculateCodeMetrics(files);
    const refactoringOpportunities = await this.identifyRefactoringOpportunities(files);
    const securityAnalysis = await this.performSecurityAnalysis(files);
    const recommendations = await this.generateRecommendations(metrics, refactoringOpportunities, securityAnalysis);

    return {
      metrics,
      refactoringOpportunities,
      securityAnalysis,
      recommendations
    };
  }

  // Calculate comprehensive code metrics
  private async calculateCodeMetrics(files: string[]): Promise<CodeMetrics> {
    let totalComplexity = 0;
    let totalMaintainability = 0;
    const codeSmells: CodeSmell[] = [];
    
    for (const file of files) {
      const analysis = await this.analyzeFile(file);
      totalComplexity += analysis.complexity;
      totalMaintainability += analysis.maintainability;
      codeSmells.push(...analysis.codeSmells);
    }

    const dependencies = await this.analyzeDependencies();
    const performance = await this.analyzePerformance();

    return {
      complexity: totalComplexity / files.length,
      maintainability: totalMaintainability / files.length,
      testCoverage: await this.calculateTestCoverage(),
      codeSmells,
      dependencies,
      performance
    };
  }

  // Analyze individual file
  private async analyzeFile(filePath: string): Promise<{
    complexity: number;
    maintainability: number;
    codeSmells: CodeSmell[];
  }> {
    const fullPath = path.resolve(this.workspaceRoot, filePath);
    const content = await fs.readFile(fullPath, 'utf-8');
    const lines = content.split('\n');

    const complexity = this.calculateCyclomaticComplexity(content);
    const maintainability = this.calculateMaintainabilityIndex(content, complexity);
    const codeSmells = this.detectCodeSmells(filePath, content, lines);

    return {
      complexity,
      maintainability,
      codeSmells
    };
  }

  // Calculate cyclomatic complexity
  private calculateCyclomaticComplexity(content: string): number {
    // Count decision points: if, while, for, case, catch, &&, ||, ?
    const patterns = [
      /\bif\s*\(/g,
      /\bwhile\s*\(/g,
      /\bfor\s*\(/g,
      /\bcase\s+/g,
      /\bcatch\s*\(/g,
      /&&/g,
      /\|\|/g,
      /\?/g
    ];

    let complexity = 1; // Base complexity
    
    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  // Calculate maintainability index
  private calculateMaintainabilityIndex(content: string, complexity: number): number {
    const lines = content.split('\n');
    const linesOfCode = lines.filter(line => line.trim().length > 0).length;
    const halsteadVolume = this.calculateHalsteadVolume(content);
    
    // Simplified maintainability index calculation
    const maintainability = Math.max(0, 
      171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode)
    );

    return Math.min(100, maintainability);
  }

  // Calculate Halstead volume (simplified)
  private calculateHalsteadVolume(content: string): number {
    const operators = content.match(/[+\-*/=<>!&|^%~?:;,(){}[\]]/g) || [];
    const operands = content.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g) || [];
    
    const uniqueOperators = new Set(operators).size;
    const uniqueOperands = new Set(operands).size;
    const totalOperators = operators.length;
    const totalOperands = operands.length;
    
    const vocabulary = uniqueOperators + uniqueOperands;
    const length = totalOperators + totalOperands;
    
    return length * Math.log2(vocabulary || 1);
  }

  // Detect code smells
  private detectCodeSmells(filePath: string, content: string, lines: string[]): CodeSmell[] {
    const smells: CodeSmell[] = [];

    // Long method detection
    const functionMatches = content.match(/function\s+\w+|const\s+\w+\s*=\s*\(/g);
    if (functionMatches) {
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('function') || lines[i].includes('=>')) {
          let braceCount = 0;
          let functionLength = 0;
          
          for (let j = i; j < lines.length; j++) {
            braceCount += (lines[j].match(/{/g) || []).length;
            braceCount -= (lines[j].match(/}/g) || []).length;
            functionLength++;
            
            if (braceCount === 0 && functionLength > 50) {
              smells.push({
                type: 'complexity',
                severity: 'medium',
                file: filePath,
                line: i + 1,
                description: `Function is too long (${functionLength} lines)`,
                suggestion: 'Consider breaking this function into smaller, more focused functions'
              });
              break;
            }
          }
        }
      }
    }

    // Duplicate code detection (simplified)
    const duplicateThreshold = 5;
    for (let i = 0; i < lines.length - duplicateThreshold; i++) {
      const block = lines.slice(i, i + duplicateThreshold).join('\n');
      const remaining = lines.slice(i + duplicateThreshold).join('\n');
      
      if (remaining.includes(block) && block.trim().length > 50) {
        smells.push({
          type: 'duplication',
          severity: 'medium',
          file: filePath,
          line: i + 1,
          description: 'Duplicate code block detected',
          suggestion: 'Extract common code into a reusable function'
        });
      }
    }

    return smells;
  }

  // Get all project files for analysis
  private async getProjectFiles(): Promise<string[]> {
    try {
      return await glob('src/**/*.{ts,tsx,js,jsx}', {
        cwd: this.workspaceRoot,
        ignore: ['node_modules/**', 'dist/**', 'out/**', '**/*.test.*', '**/*.spec.*'],
        nodir: true
      });
    } catch (error) {
      return [];
    }
  }

  // Analyze dependencies
  private async analyzeDependencies(): Promise<DependencyAnalysis> {
    const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
    let external: string[] = [];
    
    if (await fs.pathExists(packageJsonPath)) {
      const packageJson = await fs.readJson(packageJsonPath);
      external = Object.keys({
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      });
    }

    return {
      internal: [], // Would be populated by analyzing import statements
      external,
      circular: [], // Would be detected by dependency graph analysis
      unused: [], // Would be detected by usage analysis
      outdated: [] // Would be detected by version checking
    };
  }

  // Analyze performance metrics
  private async analyzePerformance(): Promise<PerformanceMetrics> {
    return {
      bundleSize: 0, // Would be calculated from build output
      loadTime: 0, // Would be measured from performance tests
      memoryUsage: 0, // Would be measured from runtime analysis
      potentialOptimizations: [
        'Consider code splitting for large bundles',
        'Implement lazy loading for components',
        'Optimize image assets',
        'Use tree shaking to eliminate dead code'
      ]
    };
  }

  // Calculate test coverage
  private async calculateTestCoverage(): Promise<number> {
    // This would integrate with coverage tools like Istanbul/NYC
    return 0; // Placeholder
  }

  // Identify refactoring opportunities
  private async identifyRefactoringOpportunities(files: string[]): Promise<RefactoringOpportunity[]> {
    const opportunities: RefactoringOpportunity[] = [];
    
    // This would analyze code patterns and suggest refactoring
    // For now, return placeholder opportunities
    
    return opportunities;
  }

  // Perform security analysis
  private async performSecurityAnalysis(files: string[]): Promise<SecurityAnalysis> {
    const vulnerabilities: SecurityVulnerability[] = [];
    
    // This would scan for common security issues
    // For now, return basic analysis
    
    return {
      vulnerabilities,
      riskScore: 0,
      recommendations: [
        'Keep dependencies up to date',
        'Use HTTPS for all external requests',
        'Validate all user inputs',
        'Implement proper authentication and authorization'
      ]
    };
  }

  // Generate recommendations based on analysis
  private async generateRecommendations(
    metrics: CodeMetrics,
    refactoring: RefactoringOpportunity[],
    security: SecurityAnalysis
  ): Promise<string[]> {
    const recommendations: string[] = [];
    
    if (metrics.complexity > 10) {
      recommendations.push('Consider reducing code complexity by breaking down large functions');
    }
    
    if (metrics.maintainability < 70) {
      recommendations.push('Improve code maintainability by adding documentation and reducing complexity');
    }
    
    if (metrics.codeSmells.length > 10) {
      recommendations.push('Address code smells to improve code quality');
    }
    
    if (security.riskScore > 5) {
      recommendations.push('Address security vulnerabilities to reduce risk');
    }
    
    return recommendations;
  }
}
