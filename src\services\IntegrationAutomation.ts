import * as fs from 'fs-extra';
import * as path from 'path';
import * as vscode from 'vscode';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Integration and Automation Interfaces
export interface AutomationTask {
  id: string;
  name: string;
  description: string;
  type: 'build' | 'test' | 'lint' | 'format' | 'deploy' | 'git' | 'package';
  command: string;
  workingDirectory?: string;
  environment?: Record<string, string>;
  dependencies?: string[];
  schedule?: string;
  enabled: boolean;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  output: string;
  error?: string;
  duration: number;
  timestamp: Date;
}

export interface WorkflowDefinition {
  name: string;
  description: string;
  trigger: 'manual' | 'file-change' | 'git-commit' | 'schedule';
  tasks: string[];
  parallel?: boolean;
  continueOnError?: boolean;
}

export interface IntegrationConfig {
  git: {
    autoCommit: boolean;
    commitMessageTemplate: string;
    autoPush: boolean;
    branchProtection: boolean;
  };
  build: {
    autoFormat: boolean;
    autoLint: boolean;
    autoTest: boolean;
    buildOnSave: boolean;
  };
  deployment: {
    environment: string;
    autoDeployBranches: string[];
    requireTests: boolean;
  };
}

export class IntegrationAutomation {
  private workspaceRoot: string;
  private tasks: Map<string, AutomationTask> = new Map();
  private workflows: Map<string, WorkflowDefinition> = new Map();
  private config: IntegrationConfig;
  private taskHistory: TaskResult[] = [];
  private watchers: vscode.FileSystemWatcher[] = [];

  constructor(workspaceRoot?: string) {
    this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    this.config = this.getDefaultConfig();
    this.initializeDefaultTasks();
    this.initializeDefaultWorkflows();
    this.setupFileWatchers();
  }

  // Task Management
  async executeTask(taskId: string): Promise<TaskResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    if (!task.enabled) {
      throw new Error(`Task ${taskId} is disabled`);
    }

    const startTime = Date.now();
    const result: TaskResult = {
      taskId,
      success: false,
      output: '',
      duration: 0,
      timestamp: new Date()
    };

    try {
      // Check dependencies
      if (task.dependencies) {
        for (const depId of task.dependencies) {
          await this.executeTask(depId);
        }
      }

      // Execute the task
      const { stdout, stderr } = await execAsync(task.command, {
        cwd: task.workingDirectory || this.workspaceRoot,
        env: { ...process.env, ...task.environment }
      });

      result.success = true;
      result.output = stdout;
      if (stderr) {
        result.output += '\nSTDERR:\n' + stderr;
      }
    } catch (error) {
      result.success = false;
      result.error = error instanceof Error ? error.message : 'Unknown error';
      result.output = error instanceof Error && 'stdout' in error ? 
        (error as any).stdout : '';
    }

    result.duration = Date.now() - startTime;
    this.taskHistory.push(result);

    // Notify VS Code
    if (result.success) {
      vscode.window.showInformationMessage(`Task ${task.name} completed successfully`);
    } else {
      vscode.window.showErrorMessage(`Task ${task.name} failed: ${result.error}`);
    }

    return result;
  }

  // Workflow Management
  async executeWorkflow(workflowName: string): Promise<TaskResult[]> {
    const workflow = this.workflows.get(workflowName);
    if (!workflow) {
      throw new Error(`Workflow ${workflowName} not found`);
    }

    const results: TaskResult[] = [];

    if (workflow.parallel) {
      // Execute tasks in parallel
      const promises = workflow.tasks.map(taskId => this.executeTask(taskId));
      const parallelResults = await Promise.allSettled(promises);
      
      for (let i = 0; i < parallelResults.length; i++) {
        const result = parallelResults[i];
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            taskId: workflow.tasks[i],
            success: false,
            output: '',
            error: result.reason,
            duration: 0,
            timestamp: new Date()
          });
        }
      }
    } else {
      // Execute tasks sequentially
      for (const taskId of workflow.tasks) {
        try {
          const result = await this.executeTask(taskId);
          results.push(result);
          
          if (!result.success && !workflow.continueOnError) {
            break;
          }
        } catch (error) {
          results.push({
            taskId,
            success: false,
            output: '',
            error: error instanceof Error ? error.message : 'Unknown error',
            duration: 0,
            timestamp: new Date()
          });
          
          if (!workflow.continueOnError) {
            break;
          }
        }
      }
    }

    return results;
  }

  // Git Integration
  async autoCommitAndPush(message?: string): Promise<void> {
    if (!this.config.git.autoCommit) return;

    const commitMessage = message || this.generateCommitMessage();
    
    try {
      // Stage all changes
      await execAsync('git add .', { cwd: this.workspaceRoot });
      
      // Commit
      await execAsync(`git commit -m "${commitMessage}"`, { cwd: this.workspaceRoot });
      
      // Push if enabled
      if (this.config.git.autoPush) {
        await execAsync('git push', { cwd: this.workspaceRoot });
      }
      
      vscode.window.showInformationMessage(`Auto-committed: ${commitMessage}`);
    } catch (error) {
      vscode.window.showErrorMessage(`Git operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Package Management
  async installDependency(packageName: string, isDev: boolean = false): Promise<void> {
    const command = `npm install ${isDev ? '--save-dev' : '--save'} ${packageName}`;
    
    try {
      await execAsync(command, { cwd: this.workspaceRoot });
      vscode.window.showInformationMessage(`Installed ${packageName}`);
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to install ${packageName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updateDependencies(): Promise<void> {
    try {
      await execAsync('npm update', { cwd: this.workspaceRoot });
      vscode.window.showInformationMessage('Dependencies updated');
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to update dependencies: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Code Quality Integration
  async runLinting(): Promise<TaskResult> {
    return this.executeTask('lint');
  }

  async runFormatting(): Promise<TaskResult> {
    return this.executeTask('format');
  }

  async runTests(): Promise<TaskResult> {
    return this.executeTask('test');
  }

  // Build and Deployment
  async buildProject(): Promise<TaskResult> {
    return this.executeTask('build');
  }

  async deployProject(environment?: string): Promise<TaskResult[]> {
    const deployWorkflow = environment ? `deploy-${environment}` : 'deploy';
    return this.executeWorkflow(deployWorkflow);
  }

  // Configuration Management
  updateConfig(newConfig: Partial<IntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  getConfig(): IntegrationConfig {
    return { ...this.config };
  }

  // Initialize default tasks
  private initializeDefaultTasks(): void {
    const defaultTasks: AutomationTask[] = [
      {
        id: 'lint',
        name: 'Lint Code',
        description: 'Run ESLint on the codebase',
        type: 'lint',
        command: 'npm run lint',
        enabled: true
      },
      {
        id: 'format',
        name: 'Format Code',
        description: 'Format code with Prettier',
        type: 'format',
        command: 'npm run format',
        enabled: true
      },
      {
        id: 'test',
        name: 'Run Tests',
        description: 'Execute all unit tests',
        type: 'test',
        command: 'npm test',
        enabled: true
      },
      {
        id: 'build',
        name: 'Build Project',
        description: 'Build the project for production',
        type: 'build',
        command: 'npm run build',
        dependencies: ['lint', 'test'],
        enabled: true
      },
      {
        id: 'type-check',
        name: 'Type Check',
        description: 'Run TypeScript type checking',
        type: 'build',
        command: 'npx tsc --noEmit',
        enabled: true
      }
    ];

    defaultTasks.forEach(task => this.tasks.set(task.id, task));
  }

  // Initialize default workflows
  private initializeDefaultWorkflows(): void {
    const defaultWorkflows: WorkflowDefinition[] = [
      {
        name: 'pre-commit',
        description: 'Pre-commit workflow',
        trigger: 'git-commit',
        tasks: ['lint', 'format', 'type-check'],
        parallel: false,
        continueOnError: false
      },
      {
        name: 'ci',
        description: 'Continuous Integration workflow',
        trigger: 'git-commit',
        tasks: ['lint', 'type-check', 'test', 'build'],
        parallel: false,
        continueOnError: false
      },
      {
        name: 'deploy',
        description: 'Deployment workflow',
        trigger: 'manual',
        tasks: ['build'],
        parallel: false,
        continueOnError: false
      }
    ];

    defaultWorkflows.forEach(workflow => this.workflows.set(workflow.name, workflow));
  }

  // Setup file watchers
  private setupFileWatchers(): void {
    if (this.config.build.buildOnSave) {
      const watcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,tsx,js,jsx}');
      
      watcher.onDidChange(async (uri) => {
        if (this.config.build.autoFormat) {
          await this.runFormatting();
        }
        if (this.config.build.autoLint) {
          await this.runLinting();
        }
      });

      this.watchers.push(watcher);
    }
  }

  // Helper methods
  private getDefaultConfig(): IntegrationConfig {
    return {
      git: {
        autoCommit: false,
        commitMessageTemplate: 'Auto-commit: ${timestamp}',
        autoPush: false,
        branchProtection: true
      },
      build: {
        autoFormat: true,
        autoLint: true,
        autoTest: false,
        buildOnSave: false
      },
      deployment: {
        environment: 'development',
        autoDeployBranches: ['main', 'master'],
        requireTests: true
      }
    };
  }

  private generateCommitMessage(): string {
    const template = this.config.git.commitMessageTemplate;
    const timestamp = new Date().toISOString();
    return template.replace('${timestamp}', timestamp);
  }

  private async saveConfig(): Promise<void> {
    const configPath = path.join(this.workspaceRoot, '.vscode', 'automation-config.json');
    await fs.ensureDir(path.dirname(configPath));
    await fs.writeJson(configPath, this.config, { spaces: 2 });
  }

  private async loadConfig(): Promise<void> {
    const configPath = path.join(this.workspaceRoot, '.vscode', 'automation-config.json');
    
    if (await fs.pathExists(configPath)) {
      try {
        const savedConfig = await fs.readJson(configPath);
        this.config = { ...this.config, ...savedConfig };
      } catch (error) {
        console.error('Failed to load automation config:', error);
      }
    }
  }

  // Enhanced automation features for IDE agent
  async createWorkflow(definition: WorkflowDefinition): Promise<string> {
    const workflowId = `workflow_${Date.now()}`;

    // Validate tasks exist
    for (const taskId of definition.tasks) {
      if (!this.tasks.has(taskId)) {
        throw new Error(`Task ${taskId} not found`);
      }
    }

    // Store workflow definition
    const workflowPath = path.join(this.workspaceRoot, '.vscode', 'workflows', `${workflowId}.json`);
    await fs.ensureDir(path.dirname(workflowPath));
    await fs.writeJson(workflowPath, definition, { spaces: 2 });

    return workflowId;
  }

  async executeWorkflowFromFile(workflowId: string): Promise<TaskResult[]> {
    const workflowPath = path.join(this.workspaceRoot, '.vscode', 'workflows', `${workflowId}.json`);

    if (!await fs.pathExists(workflowPath)) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const definition: WorkflowDefinition = await fs.readJson(workflowPath);
    const results: TaskResult[] = [];

    if (definition.parallel) {
      // Execute tasks in parallel
      const promises = definition.tasks.map(taskId => this.executeTask(taskId));
      const parallelResults = await Promise.allSettled(promises);

      parallelResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            taskId: definition.tasks[index],
            success: false,
            output: '',
            error: result.reason?.message || 'Unknown error',
            duration: 0,
            timestamp: new Date()
          });
        }
      });
    } else {
      // Execute tasks sequentially
      for (const taskId of definition.tasks) {
        try {
          const result = await this.executeTask(taskId);
          results.push(result);

          if (!result.success && !definition.continueOnError) {
            break;
          }
        } catch (error) {
          const failedResult: TaskResult = {
            taskId,
            success: false,
            output: '',
            error: error instanceof Error ? error.message : 'Unknown error',
            duration: 0,
            timestamp: new Date()
          };
          results.push(failedResult);

          if (!definition.continueOnError) {
            break;
          }
        }
      }
    }

    return results;
  }

  async setupContinuousIntegration(): Promise<void> {
    // Create basic CI/CD configuration
    const ciConfig = {
      name: 'CI/CD Pipeline',
      on: {
        push: { branches: ['main', 'develop'] },
        pull_request: { branches: ['main'] }
      },
      jobs: {
        test: {
          'runs-on': 'ubuntu-latest',
          steps: [
            { uses: 'actions/checkout@v3' },
            { uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
            { run: 'npm ci' },
            { run: 'npm run lint' },
            { run: 'npm test' },
            { run: 'npm run build' }
          ]
        }
      }
    };

    const ciPath = path.join(this.workspaceRoot, '.github', 'workflows', 'ci.yml');
    await fs.ensureDir(path.dirname(ciPath));

    // Convert to YAML format (simplified)
    const yamlContent = this.objectToYaml(ciConfig);
    await fs.writeFile(ciPath, yamlContent, 'utf-8');
  }

  async optimizeProject(): Promise<string[]> {
    const optimizations: string[] = [];

    try {
      // Check for package.json optimizations
      const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);

        // Add missing scripts
        if (!packageJson.scripts) packageJson.scripts = {};

        const recommendedScripts = {
          'lint': 'eslint src --ext .ts,.tsx,.js,.jsx',
          'lint:fix': 'eslint src --ext .ts,.tsx,.js,.jsx --fix',
          'format': 'prettier --write "src/**/*.{ts,tsx,js,jsx,json,css,md}"',
          'type-check': 'tsc --noEmit',
          'test:watch': 'npm test -- --watch',
          'test:coverage': 'npm test -- --coverage'
        };

        let scriptsAdded = false;
        for (const [script, command] of Object.entries(recommendedScripts)) {
          if (!packageJson.scripts[script]) {
            packageJson.scripts[script] = command;
            optimizations.push(`Added script: ${script}`);
            scriptsAdded = true;
          }
        }

        if (scriptsAdded) {
          await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });
        }
      }

      // Setup ESLint configuration if missing
      const eslintConfigPath = path.join(this.workspaceRoot, 'eslint.config.js');
      if (!await fs.pathExists(eslintConfigPath)) {
        const eslintConfig = `import js from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
  js.configs.recommended,
  ...tseslint.configs.recommended,
  {
    rules: {
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',
      'prefer-const': 'error',
      'no-var': 'error'
    }
  }
];`;
        await fs.writeFile(eslintConfigPath, eslintConfig, 'utf-8');
        optimizations.push('Created ESLint configuration');
      }

      // Setup Prettier configuration if missing
      const prettierConfigPath = path.join(this.workspaceRoot, '.prettierrc');
      if (!await fs.pathExists(prettierConfigPath)) {
        const prettierConfig = {
          semi: true,
          trailingComma: 'es5',
          singleQuote: true,
          printWidth: 100,
          tabWidth: 2,
          useTabs: false
        };
        await fs.writeJson(prettierConfigPath, prettierConfig, { spaces: 2 });
        optimizations.push('Created Prettier configuration');
      }

      // Setup VS Code settings
      const vscodeSettingsPath = path.join(this.workspaceRoot, '.vscode', 'settings.json');
      if (!await fs.pathExists(vscodeSettingsPath)) {
        const vscodeSettings = {
          'editor.formatOnSave': true,
          'editor.codeActionsOnSave': {
            'source.fixAll.eslint': true
          },
          'typescript.preferences.importModuleSpecifier': 'relative',
          'files.exclude': {
            '**/node_modules': true,
            '**/dist': true,
            '**/.git': true
          }
        };
        await fs.ensureDir(path.dirname(vscodeSettingsPath));
        await fs.writeJson(vscodeSettingsPath, vscodeSettings, { spaces: 2 });
        optimizations.push('Created VS Code workspace settings');
      }

    } catch (error) {
      optimizations.push(`Error during optimization: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return optimizations;
  }

  private objectToYaml(obj: any, indent = 0): string {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        yaml += this.objectToYaml(value, indent + 1);
      } else if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        value.forEach(item => {
          if (typeof item === 'object') {
            yaml += `${spaces}  -\n`;
            yaml += this.objectToYaml(item, indent + 2);
          } else {
            yaml += `${spaces}  - ${item}\n`;
          }
        });
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }

    return yaml;
  }

  // Cleanup
  dispose(): void {
    this.watchers.forEach(watcher => watcher.dispose());
    this.watchers = [];
  }
}
