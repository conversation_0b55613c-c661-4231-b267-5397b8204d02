// Browser-compatible version of MicrochipAPI for React web applications

export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface ConversationContext {
  history: ChatMessage[];
  userPreferences: Record<string, unknown>;
  sessionData: Record<string, unknown>;
  activeFile?: string;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  workspaceRoot?: string;
}

export class BrowserMicrochipAPI {
  private apiKey: string = '';
  private conversationHistory: string[] = [];
  private readonly baseURL = 'http://localhost:3001';
  private readonly endpoint = '/api/chat';
  private agentMode: boolean = false;
  private advancedAgentMode: boolean = false;
  private conversationContext: ConversationContext;
  private openaiApiKey: string = '';

  constructor() {
    this.conversationContext = {
      history: [],
      userPreferences: {},
      sessionData: {}
    };
    
    // Load stored API key
    const storedApiKey = localStorage.getItem('microchip_api_key');
    if (storedApiKey) {
      this.apiKey = storedApiKey;
    }
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey.trim();
    localStorage.setItem('microchip_api_key', this.apiKey);
  }

  setOpenAIApiKey(apiKey: string): void {
    this.openaiApiKey = apiKey.trim();
    localStorage.setItem('openai_api_key', this.openaiApiKey);
  }

  setAgentMode(enabled: boolean): void {
    this.agentMode = enabled;
    localStorage.setItem('agent_mode', enabled.toString());
  }

  setAdvancedAgentMode(enabled: boolean): void {
    this.advancedAgentMode = enabled;
    localStorage.setItem('advanced_agent_mode', enabled.toString());
  }

  getAgentMode(): boolean {
    return this.agentMode;
  }

  getAdvancedAgentMode(): boolean {
    return this.advancedAgentMode;
  }

  getApiKey(): string {
    return this.apiKey;
  }

  clearHistory(): void {
    this.conversationHistory = [];
    this.conversationContext.history = [];
    this.conversationContext.sessionData = {};
  }

  getConversationContext(): ConversationContext {
    return this.conversationContext;
  }

  // Placeholder methods for advanced features (not implemented in browser version)
  async indexWorkspace(): Promise<void> {
    console.warn('Workspace indexing not available in browser version');
  }

  async testConnection(): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key is required'
      };
    }

    try {
      const response = await this.callAPI('Hello, are you working?');
      return {
        success: true,
        message: response
      };
    } catch (error) {
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendMessage(userMessage: string): Promise<APIResponse> {
    if (!this.apiKey) {
      throw new Error('API key is required');
    }

    if (!userMessage.trim()) {
      throw new Error('Message cannot be empty');
    }

    // Add user message to conversation context
    const userChatMessage: ChatMessage = {
      id: Date.now().toString(),
      text: userMessage,
      isBot: false,
      timestamp: new Date()
    };
    this.conversationContext.history.push(userChatMessage);

    let response: string;
    let intent: string | undefined;
    let toolUsed: string | undefined;
    let confidence: number | undefined;

    // For now, use direct API call (agent features not implemented in browser version)
    response = await this.callAPI(userMessage);

    // Add bot response to conversation context
    const botChatMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      text: response,
      isBot: true,
      timestamp: new Date(),
      intent,
      toolUsed,
      confidence
    };
    this.conversationContext.history.push(botChatMessage);

    // Update legacy conversation history for backward compatibility
    this.conversationHistory.push(userMessage);

    // Keep only last 5 messages for context
    if (this.conversationHistory.length > 5) {
      this.conversationHistory = this.conversationHistory.slice(-5);
    }

    // Keep conversation context history manageable
    if (this.conversationContext.history.length > 20) {
      this.conversationContext.history = this.conversationContext.history.slice(-20);
    }

    return {
      success: true,
      message: response,
      intent,
      toolUsed,
      confidence
    };
  }

  private async callAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5),
      category: 101,
      logQnA: true,
      client: "react-chatbot",
      apiKey: this.apiKey
    };

    try {
      const response = await fetch(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      const data = await response.json() as {
        success?: boolean;
        message?: string;
        error?: string;
      };

      if (!response.ok) {
        if (data.error) {
          throw new Error(data.error);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      if (data.success && data.message) {
        return data.message;
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError' || error.name === 'TimeoutError') {
          throw new Error('Request timeout - Server took too long to respond');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - Please check if the backend server is running on port 3001');
        } else {
          throw error;
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }
}

// Create a singleton instance
export const browserMicrochipAPI = new BrowserMicrochipAPI();
