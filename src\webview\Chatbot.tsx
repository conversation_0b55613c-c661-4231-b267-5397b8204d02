import React, { useState, useRef, useEffect } from 'react';
import { webviewMicrochipAPI, type ChatMessage } from '../services/WebviewMicrochipAPI';
import '../components/Chatbot.css';

interface ChatbotProps {
  onDisconnect: () => void;
  apiKey: string;
}

export const Chatbot: React.FC<ChatbotProps> = ({ onDisconnect, apiKey }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?",
      isBot: true,
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [agentMode, setAgentMode] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Set API key when component mounts
  useEffect(() => {
    if (apiKey) {
      webviewMicrochipAPI.setApiKey(apiKey);
    }
  }, [apiKey]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Focus input on mount
    inputRef.current?.focus();
  }, []);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputMessage.trim() || isLoading) {
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setError('');

    try {
      const response = await webviewMicrochipAPI.sendMessage(userMessage.text);

      if (response.success) {
        const botMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: response.message,
          isBot: true,
          timestamp: new Date(),
          intent: response.intent,
          toolUsed: response.toolUsed,
          confidence: response.confidence,
          reasoning: response.reasoning,
          toolExecutionSteps: response.toolExecutionSteps
        };

        setMessages(prev => [...prev, botMessage]);
      } else {
        throw new Error(response.error || 'API request failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      
      // Add error message to chat
      const errorChatMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Sorry, I encountered an error: ${errorMessage}`,
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorChatMessage]);

      // Show error via VS Code
      if (window.vscode) {
        window.vscode.postMessage({
          type: 'showError',
          message: `Chat error: ${errorMessage}`
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = () => {
    const welcomeMessage = agentMode
      ? "Hello! I'm your Microchip AI assistant in Agent Mode. I'll analyze your questions and use specialized tools to provide better assistance. I can help with code analysis, documentation, project setup, troubleshooting, and general microcontroller questions. What would you like to know?"
      : "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?";

    setMessages([
      {
        id: '1',
        text: welcomeMessage,
        isBot: true,
        timestamp: new Date()
      }
    ]);
    webviewMicrochipAPI.clearConversationHistory();
    setError('');
  };

  const handleAgentModeToggle = () => {
    setAgentMode(!agentMode);
    // Update welcome message when mode changes
    const welcomeMessage = !agentMode
      ? "Agent Mode activated! I'll now analyze your questions and use specialized tools for better assistance. I can help with code analysis, documentation, project setup, troubleshooting, and general microcontroller questions."
      : "Agent Mode deactivated. I'll now provide direct responses from the Microchip AI API.";

    const modeChangeMessage: ChatMessage = {
      id: Date.now().toString(),
      text: welcomeMessage,
      isBot: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, modeChangeMessage]);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="chatbot-container">
      <div className="chatbot-header">
        <div className="header-info">
          <h1>🤖 Microchip AI Chatbot</h1>
          <span className="status">
            Connected {agentMode && <span className="agent-badge">🧠 Agent Mode</span>}
          </span>
        </div>
        <div className="header-actions">
          <button
            onClick={handleAgentModeToggle}
            className={`agent-toggle ${agentMode ? 'active' : ''}`}
            title={agentMode ? "Disable Agent Mode" : "Enable Agent Mode"}
          >
            🧠 {agentMode ? 'Agent ON' : 'Agent OFF'}
          </button>
          <button
            onClick={handleClearChat}
            className="clear-button"
            title="Clear chat history"
          >
            🗑️ Clear
          </button>
          <button
            onClick={onDisconnect}
            className="disconnect-button"
            title="Change API key"
          >
            🔑 Change Key
          </button>
        </div>
      </div>

      <div className="messages-container">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message ${message.isBot ? 'bot-message' : 'user-message'}`}
          >
            <div className="message-content">
              <div className="message-text">
                {message.text}
              </div>
              {message.isBot && agentMode && (message.intent || message.toolUsed) && (
                <div className="agent-info">
                  {message.intent && (
                    <span className="intent-badge" title="Detected Intent">
                      🎯 {message.intent}
                    </span>
                  )}
                  {message.toolUsed && (
                    <span className="tool-badge" title="Tool Used">
                      🔧 {message.toolUsed}
                    </span>
                  )}
                  {message.confidence && (
                    <span className="confidence-badge" title="Confidence Score">
                      📊 {Math.round(message.confidence * 100)}%
                    </span>
                  )}
                </div>
              )}
              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message bot-message">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>
      )}

      <form onSubmit={handleSendMessage} className="input-form">
        <div className="input-container">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Type your message here..."
            disabled={isLoading}
            className="message-input"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="send-button"
          >
            {isLoading ? '⏳' : '📤'}
          </button>
        </div>
      </form>
    </div>
  );
};
