"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomatedTestingService = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const vscode = __importStar(require("vscode"));
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class AutomatedTestingService {
    constructor(workspaceRoot) {
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.config = this.detectTestConfiguration();
    }
    detectTestConfiguration() {
        const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
        let framework = 'jest';
        let testDir = 'src/__tests__';
        let testPattern = '**/*.{test,spec}.{ts,tsx,js,jsx}';
        try {
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = fs.readJsonSync(packageJsonPath);
                const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
                if (dependencies.vitest)
                    framework = 'vitest';
                else if (dependencies.mocha)
                    framework = 'mocha';
                else if (dependencies.cypress)
                    framework = 'cypress';
                // Check for custom test configuration
                if (packageJson.jest?.testMatch) {
                    testPattern = packageJson.jest.testMatch[0] || testPattern;
                }
            }
        }
        catch (error) {
            console.warn('Could not read package.json for test configuration');
        }
        return {
            framework,
            testDir,
            testPattern,
            coverage: true,
            watch: false
        };
    }
    async runTests(options) {
        const config = { ...this.config, ...options };
        const startTime = Date.now();
        try {
            let command = this.buildTestCommand(config);
            const { stdout, stderr } = await execAsync(command, {
                cwd: this.workspaceRoot,
                timeout: 300000 // 5 minutes
            });
            const duration = Date.now() - startTime;
            const result = this.parseTestOutput(stdout, stderr, config.framework);
            return {
                ...result,
                duration,
                output: stdout + (stderr ? `\n${stderr}` : '')
            };
        }
        catch (error) {
            return {
                success: false,
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                skippedTests: 0,
                duration: Date.now() - startTime,
                output: error instanceof Error ? error.message : 'Unknown error',
                errors: [{
                        file: 'unknown',
                        test: 'test execution',
                        message: error instanceof Error ? error.message : 'Test execution failed'
                    }]
            };
        }
    }
    buildTestCommand(config) {
        switch (config.framework) {
            case 'jest':
                let jestCmd = 'npm test';
                if (config.coverage)
                    jestCmd += ' -- --coverage';
                if (config.watch)
                    jestCmd += ' -- --watch';
                return jestCmd;
            case 'vitest':
                let vitestCmd = 'npx vitest run';
                if (config.coverage)
                    vitestCmd += ' --coverage';
                if (config.watch)
                    vitestCmd = 'npx vitest';
                return vitestCmd;
            case 'mocha':
                return `npx mocha "${config.testPattern}"`;
            case 'cypress':
                return 'npx cypress run';
            default:
                return 'npm test';
        }
    }
    parseTestOutput(stdout, stderr, framework) {
        const errors = [];
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;
        let skippedTests = 0;
        let coverage;
        try {
            switch (framework) {
                case 'jest':
                case 'vitest':
                    const testMatch = stdout.match(/Tests:\s*(\d+)\s*failed,\s*(\d+)\s*passed,\s*(\d+)\s*total/);
                    if (testMatch) {
                        failedTests = parseInt(testMatch[1]);
                        passedTests = parseInt(testMatch[2]);
                        totalTests = parseInt(testMatch[3]);
                    }
                    // Parse coverage
                    const coverageMatch = stdout.match(/All files\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)/);
                    if (coverageMatch) {
                        coverage = {
                            statements: parseFloat(coverageMatch[1]),
                            branches: parseFloat(coverageMatch[2]),
                            functions: parseFloat(coverageMatch[3]),
                            lines: parseFloat(coverageMatch[4])
                        };
                    }
                    break;
                case 'mocha':
                    const mochaMatch = stdout.match(/(\d+)\s*passing/);
                    const mochaFailMatch = stdout.match(/(\d+)\s*failing/);
                    if (mochaMatch)
                        passedTests = parseInt(mochaMatch[1]);
                    if (mochaFailMatch)
                        failedTests = parseInt(mochaFailMatch[1]);
                    totalTests = passedTests + failedTests;
                    break;
            }
            // Parse errors from stderr
            if (stderr) {
                const errorLines = stderr.split('\n').filter(line => line.trim());
                errorLines.forEach(line => {
                    if (line.includes('Error:') || line.includes('Failed:')) {
                        errors.push({
                            file: 'unknown',
                            test: 'unknown',
                            message: line.trim()
                        });
                    }
                });
            }
        }
        catch (parseError) {
            console.warn('Failed to parse test output:', parseError);
        }
        return {
            success: failedTests === 0 && totalTests > 0,
            totalTests,
            passedTests,
            failedTests,
            skippedTests,
            coverage,
            errors
        };
    }
    async generateTests(request) {
        const targetPath = path.resolve(this.workspaceRoot, request.targetFile);
        const testFileName = this.generateTestFileName(request.targetFile, request.framework);
        const testPath = path.join(this.config.testDir, testFileName);
        // Read the target file to analyze
        let targetContent = '';
        try {
            targetContent = await fs.readFile(targetPath, 'utf-8');
        }
        catch (error) {
            throw new Error(`Could not read target file: ${request.targetFile}`);
        }
        // Generate test content based on framework and type
        const content = this.generateTestContent(request, targetContent);
        const dependencies = this.getTestDependencies(request.framework, request.testType);
        const setupInstructions = this.getSetupInstructions(request);
        return {
            testFile: testPath,
            content,
            dependencies,
            setupInstructions
        };
    }
    generateTestFileName(targetFile, framework) {
        const ext = path.extname(targetFile);
        const base = path.basename(targetFile, ext);
        const dir = path.dirname(targetFile);
        const testExt = framework === 'cypress' ? '.cy' : '.test';
        return path.join(dir, `${base}${testExt}${ext}`);
    }
    generateTestContent(request, targetContent) {
        const className = this.extractClassName(targetContent);
        const functions = this.extractFunctions(targetContent);
        switch (request.framework) {
            case 'jest':
                return this.generateJestTest(request, className, functions);
            case 'vitest':
                return this.generateVitestTest(request, className, functions);
            case 'mocha':
                return this.generateMochaTest(request, className, functions);
            case 'cypress':
                return this.generateCypressTest(request);
            default:
                return this.generateJestTest(request, className, functions);
        }
    }
    extractClassName(content) {
        const classMatch = content.match(/class\s+(\w+)/);
        const exportMatch = content.match(/export\s+(?:default\s+)?(?:class\s+)?(\w+)/);
        return classMatch?.[1] || exportMatch?.[1] || 'UnknownClass';
    }
    extractFunctions(content) {
        const functionMatches = content.match(/(?:function\s+|const\s+\w+\s*=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\w+)|async\s+\w+\s*\(|\w+\s*\([^)]*\)\s*{)/g);
        return functionMatches?.map(match => {
            const nameMatch = match.match(/(?:function\s+|const\s+|async\s+)?(\w+)/);
            return nameMatch?.[1] || 'unknownFunction';
        }) || [];
    }
    generateJestTest(request, className, functions) {
        const imports = request.includeMocks ?
            `import { jest } from '@jest/globals';\nimport { ${className} } from '${request.targetFile.replace(/\.(ts|tsx)$/, '')}';\n` :
            `import { ${className} } from '${request.targetFile.replace(/\.(ts|tsx)$/, '')}';\n`;
        const setup = request.includeSetup ? `
  beforeEach(() => {
    // Setup before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Cleanup after each test
  });
` : '';
        const testCases = functions.map(func => `
  describe('${func}', () => {
    it('should work correctly', () => {
      // TODO: Implement test for ${func}
      expect(true).toBe(true);
    });

    it('should handle errors gracefully', () => {
      // TODO: Test error handling for ${func}
      expect(true).toBe(true);
    });
  });`).join('\n');
        return `${imports}
describe('${className}', () => {${setup}${testCases}
});`;
    }
    generateVitestTest(request, className, functions) {
        return this.generateJestTest(request, className, functions)
            .replace('@jest/globals', 'vitest')
            .replace('jest.', 'vi.');
    }
    generateMochaTest(request, className, functions) {
        const imports = `import { expect } from 'chai';\nimport { ${className} } from '${request.targetFile.replace(/\.(ts|tsx)$/, '')}';\n`;
        const testCases = functions.map(func => `
  describe('${func}', () => {
    it('should work correctly', () => {
      // TODO: Implement test for ${func}
      expect(true).to.be.true;
    });
  });`).join('\n');
        return `${imports}
describe('${className}', () => {${testCases}
});`;
    }
    generateCypressTest(request) {
        return `describe('${path.basename(request.targetFile)} E2E Tests', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should load the page', () => {
    cy.get('body').should('be.visible');
  });

  it('should interact with components', () => {
    // TODO: Add specific component interactions
    cy.get('[data-testid="main-component"]').should('exist');
  });
});`;
    }
    getTestDependencies(framework, testType) {
        const baseDeps = ['@types/node'];
        switch (framework) {
            case 'jest':
                return [...baseDeps, 'jest', '@types/jest'];
            case 'vitest':
                return [...baseDeps, 'vitest'];
            case 'mocha':
                return [...baseDeps, 'mocha', 'chai', '@types/mocha', '@types/chai'];
            case 'cypress':
                return [...baseDeps, 'cypress'];
            default:
                return baseDeps;
        }
    }
    getSetupInstructions(request) {
        const instructions = [
            `Install test dependencies: npm install --save-dev ${this.getTestDependencies(request.framework, request.testType).join(' ')}`,
            `Create test directory: ${this.config.testDir}`,
            'Configure test scripts in package.json'
        ];
        if (request.includeMocks) {
            instructions.push('Set up mocking utilities');
        }
        if (request.testType === 'e2e') {
            instructions.push('Configure E2E test environment');
        }
        return instructions;
    }
    async runSpecificTest(testFile) {
        const relativePath = path.relative(this.workspaceRoot, testFile);
        return this.runTests({ testPattern: relativePath });
    }
    async watchTests() {
        const command = this.buildTestCommand({ ...this.config, watch: true });
        const child = (0, child_process_1.exec)(command, { cwd: this.workspaceRoot });
        child.stdout?.on('data', (data) => {
            console.log(data.toString());
        });
        child.stderr?.on('data', (data) => {
            console.error(data.toString());
        });
    }
}
exports.AutomatedTestingService = AutomatedTestingService;
//# sourceMappingURL=AutomatedTestingService.js.map