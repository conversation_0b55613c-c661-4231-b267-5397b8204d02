"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyInput = void 0;
const react_1 = __importStar(require("react"));
const MicrochipAPI_1 = require("../services/MicrochipAPI");
require("../components/ApiKeyInput.css");
const ApiKeyInput = ({ onApiKeySet, initialApiKey = '' }) => {
    const [apiKey, setApiKey] = (0, react_1.useState)(initialApiKey);
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)('');
    const [showKey, setShowKey] = (0, react_1.useState)(false);
    (0, react_1.useEffect)(() => {
        setApiKey(initialApiKey);
    }, [initialApiKey]);
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!apiKey.trim()) {
            setError('Please enter an API key');
            return;
        }
        setIsLoading(true);
        setError('');
        try {
            MicrochipAPI_1.microchipAPI.setApiKey(apiKey);
            const result = await MicrochipAPI_1.microchipAPI.testConnection();
            if (result.success) {
                onApiKeySet(true, apiKey);
                // Show success message via VS Code
                if (window.vscode) {
                    window.vscode.postMessage({
                        type: 'showInfo',
                        message: 'Successfully connected to Microchip AI API!'
                    });
                }
            }
            else {
                setError(result.error || 'Failed to connect to API');
                onApiKeySet(false);
            }
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
            setError(errorMessage);
            onApiKeySet(false);
            // Show error via VS Code
            if (window.vscode) {
                window.vscode.postMessage({
                    type: 'showError',
                    message: `Connection failed: ${errorMessage}`
                });
            }
        }
        finally {
            setIsLoading(false);
        }
    };
    const handleKeyChange = (e) => {
        setApiKey(e.target.value);
        if (error)
            setError(''); // Clear error when user starts typing
    };
    return (<div className="api-key-container">
      <div className="api-key-card">
        <div className="header">
          <h1>🤖 Microchip AI Chatbot</h1>
          <p>Enter your API key to start chatting</p>
        </div>

        <form onSubmit={handleSubmit} className="api-key-form">
          <div className="input-group">
            <label htmlFor="apiKey">API Key</label>
            <div className="input-wrapper">
              <input id="apiKey" type={showKey ? 'text' : 'password'} value={apiKey} onChange={handleKeyChange} placeholder="Enter your Microchip API key" disabled={isLoading} className={error ? 'error' : ''}/>
              <button type="button" className="toggle-visibility" onClick={() => setShowKey(!showKey)} disabled={isLoading} aria-label={showKey ? 'Hide API key' : 'Show API key'}>
                {showKey ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          </div>

          {error && (<div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>)}

          <button type="submit" disabled={isLoading || !apiKey.trim()} className="connect-button">
            {isLoading ? (<>
                <span className="spinner"></span>
                Testing Connection...
              </>) : (<>
                🔗 Connect
              </>)}
          </button>
        </form>

        <div className="info-section">
          <h3>ℹ️ About</h3>
          <p>
            This chatbot connects to the Microchip AI API to help you with questions about 
            microcontrollers, development tools, and products.
          </p>
          <ul>
            <li>Your API key is stored securely in VS Code and never shared</li>
            <li>Conversations are sent to Microchip's servers for processing</li>
            <li>Connection is tested before enabling the chat interface</li>
          </ul>
        </div>
      </div>
    </div>);
};
exports.ApiKeyInput = ApiKeyInput;
//# sourceMappingURL=ApiKeyInput.js.map