"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalIntegrationService = void 0;
const vscode = __importStar(require("vscode"));
const child_process_1 = require("child_process");
const util_1 = require("util");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class TerminalIntegrationService {
    constructor(workspaceRoot) {
        this.terminals = new Map();
        this.runningProcesses = new Map();
        this.buildConfigurations = [];
        this.scripts = [];
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.initializeScripts();
        this.loadBuildConfigurations();
    }
    async initializeScripts() {
        // Load scripts from package.json
        const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
        try {
            if (await fs.pathExists(packageJsonPath)) {
                const packageJson = await fs.readJson(packageJsonPath);
                if (packageJson.scripts) {
                    Object.entries(packageJson.scripts).forEach(([name, command]) => {
                        this.scripts.push({
                            name,
                            command: command,
                            description: `NPM script: ${name}`,
                            category: this.categorizeScript(name)
                        });
                    });
                }
            }
        }
        catch (error) {
            console.warn('Failed to load package.json scripts:', error);
        }
        // Add common development scripts
        this.scripts.push({
            name: 'install',
            command: 'npm install',
            description: 'Install project dependencies',
            category: 'utility'
        }, {
            name: 'clean',
            command: 'rm -rf node_modules dist build',
            description: 'Clean build artifacts and dependencies',
            category: 'utility'
        }, {
            name: 'type-check',
            command: 'tsc --noEmit',
            description: 'Run TypeScript type checking',
            category: 'build'
        });
    }
    categorizeScript(scriptName) {
        if (scriptName.includes('build') || scriptName.includes('compile'))
            return 'build';
        if (scriptName.includes('test') || scriptName.includes('spec'))
            return 'test';
        if (scriptName.includes('dev') || scriptName.includes('start') || scriptName.includes('serve'))
            return 'dev';
        if (scriptName.includes('deploy') || scriptName.includes('publish'))
            return 'deploy';
        return 'utility';
    }
    async loadBuildConfigurations() {
        // Default build configurations
        this.buildConfigurations = [
            {
                name: 'Development Build',
                command: 'npm',
                args: ['run', 'build:dev'],
                prebuild: ['npm run lint'],
                postbuild: ['npm run test']
            },
            {
                name: 'Production Build',
                command: 'npm',
                args: ['run', 'build'],
                prebuild: ['npm run lint', 'npm run test'],
                postbuild: ['npm run package']
            },
            {
                name: 'VS Code Extension Build',
                command: 'npm',
                args: ['run', 'compile'],
                prebuild: ['npm run lint'],
                postbuild: ['npm run build:webview']
            }
        ];
    }
    async executeCommand(command, options) {
        const startTime = Date.now();
        try {
            // Security check for allowed commands
            if (!this.isCommandAllowed(command)) {
                throw new Error(`Command "${command}" is not allowed for security reasons`);
            }
            const execOptions = {
                cwd: options?.cwd || this.workspaceRoot,
                env: { ...process.env, ...options?.env },
                timeout: options?.timeout || 60000,
                shell: options?.shell !== false ? (process.platform === 'win32' ? 'powershell.exe' : '/bin/bash') : undefined
            };
            const { stdout, stderr } = await execAsync(command, execOptions);
            const duration = Date.now() - startTime;
            return {
                success: true,
                output: stdout + (stderr ? `\n${stderr}` : ''),
                exitCode: 0,
                duration
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            return {
                success: false,
                output: error.stdout || '',
                error: error.stderr || error.message,
                exitCode: error.code || 1,
                duration
            };
        }
    }
    isCommandAllowed(command) {
        const allowedCommands = [
            'npm', 'yarn', 'pnpm', 'node', 'tsc', 'eslint', 'prettier',
            'git', 'code', 'vsce', 'jest', 'vitest', 'mocha', 'cypress',
            'webpack', 'vite', 'rollup', 'esbuild', 'babel'
        ];
        const commandStart = command.trim().split(' ')[0];
        return allowedCommands.some(allowed => commandStart === allowed || commandStart.endsWith(`/${allowed}`) || commandStart.endsWith(`\\${allowed}`));
    }
    async runScript(scriptName) {
        const script = this.scripts.find(s => s.name === scriptName);
        if (!script) {
            throw new Error(`Script "${scriptName}" not found`);
        }
        // Run dependencies first
        if (script.dependencies) {
            for (const dep of script.dependencies) {
                const depResult = await this.runScript(dep);
                if (!depResult.success) {
                    throw new Error(`Dependency script "${dep}" failed: ${depResult.error}`);
                }
            }
        }
        return this.executeCommand(script.command);
    }
    async runBuild(configName) {
        const config = configName
            ? this.buildConfigurations.find(c => c.name === configName)
            : this.buildConfigurations[0];
        if (!config) {
            throw new Error(`Build configuration "${configName}" not found`);
        }
        try {
            // Run prebuild scripts
            if (config.prebuild) {
                for (const prebuildCmd of config.prebuild) {
                    const result = await this.executeCommand(prebuildCmd, { cwd: config.cwd });
                    if (!result.success) {
                        throw new Error(`Prebuild failed: ${result.error}`);
                    }
                }
            }
            // Run main build
            const buildCommand = `${config.command} ${config.args.join(' ')}`;
            const buildResult = await this.executeCommand(buildCommand, {
                cwd: config.cwd,
                env: config.env
            });
            if (!buildResult.success) {
                throw new Error(`Build failed: ${buildResult.error}`);
            }
            // Run postbuild scripts
            if (config.postbuild) {
                for (const postbuildCmd of config.postbuild) {
                    const result = await this.executeCommand(postbuildCmd, { cwd: config.cwd });
                    if (!result.success) {
                        console.warn(`Postbuild warning: ${result.error}`);
                    }
                }
            }
            return buildResult;
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Build failed',
                exitCode: 1,
                duration: 0
            };
        }
    }
    createTerminal(name) {
        const terminal = vscode.window.createTerminal({
            name,
            cwd: this.workspaceRoot
        });
        const session = {
            id: `terminal_${Date.now()}`,
            name,
            terminal,
            processes: [],
            history: []
        };
        this.terminals.set(session.id, session);
        return session;
    }
    async runInTerminal(sessionId, command) {
        const session = this.terminals.get(sessionId);
        if (!session) {
            throw new Error(`Terminal session "${sessionId}" not found`);
        }
        session.terminal.sendText(command);
        session.history.push(command);
    }
    async spawnProcess(command, args, options) {
        return new Promise((resolve, reject) => {
            const spawnOptions = {
                cwd: options?.cwd || this.workspaceRoot,
                env: { ...process.env, ...options?.env },
                detached: options?.detached || false,
                stdio: 'pipe'
            };
            const childProcess = (0, child_process_1.spawn)(command, args, spawnOptions);
            const processInfo = {
                pid: childProcess.pid,
                command,
                args,
                cwd: spawnOptions.cwd,
                startTime: new Date(),
                status: 'running'
            };
            this.runningProcesses.set(processInfo.pid, childProcess);
            childProcess.on('close', (code) => {
                processInfo.status = code === 0 ? 'completed' : 'failed';
                this.runningProcesses.delete(processInfo.pid);
            });
            childProcess.on('error', (error) => {
                processInfo.status = 'failed';
                this.runningProcesses.delete(processInfo.pid);
                reject(error);
            });
            resolve(processInfo);
        });
    }
    async killProcess(pid) {
        const process = this.runningProcesses.get(pid);
        if (process) {
            process.kill();
            this.runningProcesses.delete(pid);
            return true;
        }
        return false;
    }
    getRunningProcesses() {
        return Array.from(this.runningProcesses.entries()).map(([pid, process]) => ({
            pid,
            command: process.spawnfile,
            args: process.spawnargs.slice(1),
            cwd: process.spawnargs[0] || this.workspaceRoot,
            startTime: new Date(), // Would need to track this separately
            status: 'running'
        }));
    }
    async installDependencies(packageManager = 'npm') {
        const commands = {
            npm: 'npm install',
            yarn: 'yarn install',
            pnpm: 'pnpm install'
        };
        return this.executeCommand(commands[packageManager]);
    }
    async addDependency(packageName, options) {
        const pm = options?.packageManager || 'npm';
        const devFlag = options?.dev ? (pm === 'npm' ? '--save-dev' : '-D') : '';
        const version = options?.version ? `@${options.version}` : '';
        const commands = {
            npm: `npm install ${packageName}${version} ${devFlag}`.trim(),
            yarn: `yarn add ${packageName}${version} ${devFlag}`.trim(),
            pnpm: `pnpm add ${packageName}${version} ${devFlag}`.trim()
        };
        return this.executeCommand(commands[pm]);
    }
    async removeDependency(packageName, packageManager = 'npm') {
        const commands = {
            npm: `npm uninstall ${packageName}`,
            yarn: `yarn remove ${packageName}`,
            pnpm: `pnpm remove ${packageName}`
        };
        return this.executeCommand(commands[packageManager]);
    }
    getAvailableScripts() {
        return [...this.scripts];
    }
    getBuildConfigurations() {
        return [...this.buildConfigurations];
    }
    addBuildConfiguration(config) {
        this.buildConfigurations.push(config);
    }
    async openTerminal(name) {
        const session = this.createTerminal(name || 'AI Assistant Terminal');
        session.terminal.show();
        return session;
    }
    async runDevServer() {
        const devScripts = this.scripts.filter(s => s.category === 'dev' ||
            s.name.includes('dev') ||
            s.name.includes('start') ||
            s.name.includes('serve'));
        if (devScripts.length === 0) {
            throw new Error('No development server script found');
        }
        return this.runScript(devScripts[0].name);
    }
    async runTests(testType) {
        let testScript = 'test';
        if (testType) {
            const specificScript = this.scripts.find(s => s.name.includes(testType) || s.name === `test:${testType}`);
            if (specificScript) {
                testScript = specificScript.name;
            }
        }
        return this.runScript(testScript);
    }
    dispose() {
        // Kill all running processes
        this.runningProcesses.forEach((process, pid) => {
            process.kill();
        });
        this.runningProcesses.clear();
        // Dispose terminals
        this.terminals.forEach(session => {
            session.terminal.dispose();
        });
        this.terminals.clear();
    }
}
exports.TerminalIntegrationService = TerminalIntegrationService;
//# sourceMappingURL=TerminalIntegrationService.js.map