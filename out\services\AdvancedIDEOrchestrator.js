"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedIDEOrchestrator = void 0;
const vscode = __importStar(require("vscode"));
const AdvancedAgentTools_1 = require("./AdvancedAgentTools");
const AdvancedCodeAnalysis_1 = require("./AdvancedCodeAnalysis");
const IntelligentSearch_1 = require("./IntelligentSearch");
const IntelligentCodeGeneration_1 = require("./IntelligentCodeGeneration");
const AutomatedRefactoring_1 = require("./AutomatedRefactoring");
const ErrorDetectionResolution_1 = require("./ErrorDetectionResolution");
const IntegrationAutomation_1 = require("./IntegrationAutomation");
const UserInteractionIntelligence_1 = require("./UserInteractionIntelligence");
const LangChainAgentService_1 = require("./LangChainAgentService");
const AutomatedTestingService_1 = require("./AutomatedTestingService");
const GitIntegrationService_1 = require("./GitIntegrationService");
const TerminalIntegrationService_1 = require("./TerminalIntegrationService");
class AdvancedIDEOrchestrator {
    constructor(workspaceRoot) {
        this.services = new Map();
        this.commands = new Map();
        this.isInitialized = false;
        const root = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.workspace = {
            root,
            type: 'extension',
            language: 'typescript',
            capabilities: []
        };
        this.initializeServices();
        this.registerCommands();
    }
    // Initialize all advanced IDE services
    async initialize() {
        if (this.isInitialized)
            return;
        try {
            // Initialize core services
            await this.langChainAgent.initialize();
            // Analyze workspace
            await this.analyzeWorkspace();
            // Setup integrations
            await this.setupIntegrations();
            this.isInitialized = true;
            vscode.window.showInformationMessage('🚀 Advanced IDE capabilities initialized successfully!');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to initialize IDE capabilities: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    // Execute intelligent command
    async executeCommand(commandId, args) {
        const command = this.commands.get(commandId);
        if (!command) {
            throw new Error(`Command ${commandId} not found`);
        }
        try {
            const result = await command.handler(args);
            // Log command execution for learning
            await this.userInteraction.trackFeedback(commandId, 'helpful');
            return result;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    // Intelligent code assistance
    async provideCodeAssistance(query) {
        // Analyze user intent
        const intent = await this.userInteraction.analyzeUserInput(query);
        // Route to appropriate service based on intent
        let response;
        switch (intent.category) {
            case 'code':
                response = await this.handleCodeRequest(query, intent);
                break;
            case 'debug':
                response = await this.handleDebugRequest(query, intent);
                break;
            case 'refactor':
                response = await this.handleRefactorRequest(query, intent);
                break;
            case 'generate':
                response = await this.handleGenerateRequest(query, intent);
                break;
            case 'search':
                response = await this.handleSearchRequest(query, intent);
                break;
            default:
                response = (await this.langChainAgent.executeQuery(query)).output;
        }
        return this.userInteraction.generateResponse(intent, response);
    }
    // Get proactive suggestions
    async getProactiveSuggestions() {
        const suggestions = await this.userInteraction.getProactiveSuggestions();
        // Add technical suggestions
        const diagnostics = await this.errorDetection.detectErrors();
        if (diagnostics.errors.length > 0) {
            suggestions.push({
                id: 'fix-errors',
                type: 'warning',
                title: `${diagnostics.errors.length} issues detected`,
                description: 'Run error detection and get automated fixes',
                action: 'ide.detectErrors',
                priority: 'high',
                relevanceScore: 0.9,
                dismissible: false
            });
        }
        return suggestions;
    }
    // Initialize services
    initializeServices() {
        this.agentTools = new AdvancedAgentTools_1.AdvancedAgentTools(this.workspace.root);
        this.codeAnalysis = new AdvancedCodeAnalysis_1.AdvancedCodeAnalysis(this.workspace.root);
        this.intelligentSearch = new IntelligentSearch_1.IntelligentSearch(this.workspace.root);
        this.codeGeneration = new IntelligentCodeGeneration_1.IntelligentCodeGeneration(this.workspace.root);
        this.refactoring = new AutomatedRefactoring_1.AutomatedRefactoring(this.workspace.root);
        this.errorDetection = new ErrorDetectionResolution_1.ErrorDetectionResolution(this.workspace.root);
        this.automation = new IntegrationAutomation_1.IntegrationAutomation(this.workspace.root);
        this.userInteraction = new UserInteractionIntelligence_1.UserInteractionIntelligence();
        this.langChainAgent = new LangChainAgentService_1.LangChainAgentService(process.env.OPENAI_API_KEY || '', this.workspace.root, {
            history: [],
            userPreferences: {},
            sessionData: {},
            workspaceRoot: this.workspace.root
        });
        this.testingService = new AutomatedTestingService_1.AutomatedTestingService(this.workspace.root);
        this.gitService = new GitIntegrationService_1.GitIntegrationService(this.workspace.root);
        this.terminalService = new TerminalIntegrationService_1.TerminalIntegrationService(this.workspace.root);
        // Register services
        this.services.set('agentTools', this.agentTools);
        this.services.set('codeAnalysis', this.codeAnalysis);
        this.services.set('intelligentSearch', this.intelligentSearch);
        this.services.set('codeGeneration', this.codeGeneration);
        this.services.set('refactoring', this.refactoring);
        this.services.set('errorDetection', this.errorDetection);
        this.services.set('automation', this.automation);
        this.services.set('userInteraction', this.userInteraction);
        this.services.set('langChainAgent', this.langChainAgent);
        this.services.set('testingService', this.testingService);
        this.services.set('gitService', this.gitService);
        this.services.set('terminalService', this.terminalService);
    }
    // Register IDE commands
    registerCommands() {
        const commands = [
            {
                id: 'ide.analyzeProject',
                name: 'Analyze Project',
                description: 'Comprehensive project analysis with metrics and recommendations',
                category: 'analysis',
                handler: () => this.codeAnalysis.analyzeProject()
            },
            {
                id: 'ide.intelligentSearch',
                name: 'Intelligent Search',
                description: 'Multi-modal search with semantic understanding',
                category: 'search',
                handler: (query) => this.intelligentSearch.search(query)
            },
            {
                id: 'ide.generateCode',
                name: 'Generate Code',
                description: 'Context-aware code generation',
                category: 'generation',
                handler: (request) => this.codeGeneration.generateCode(request)
            },
            {
                id: 'ide.refactorCode',
                name: 'Refactor Code',
                description: 'Automated refactoring with intelligent suggestions',
                category: 'refactoring',
                handler: (filePath) => this.refactoring.analyzeForRefactoring(filePath)
            },
            {
                id: 'ide.detectErrors',
                name: 'Detect Errors',
                description: 'Proactive error detection with automated fixes',
                category: 'analysis',
                handler: (filePath) => this.errorDetection.detectErrors(filePath)
            },
            {
                id: 'ide.runAutomation',
                name: 'Run Automation',
                description: 'Execute automation workflows',
                category: 'automation',
                handler: (taskId) => this.automation.executeTask(taskId)
            },
            {
                id: 'ide.executeCommand',
                name: 'Execute Command',
                description: 'Execute terminal commands safely',
                category: 'automation',
                handler: (command) => this.executeTerminalCommand(command)
            },
            {
                id: 'ide.optimizeProject',
                name: 'Optimize Project',
                description: 'Apply project optimizations and best practices',
                category: 'automation',
                handler: () => this.automation.optimizeProject()
            },
            {
                id: 'ide.setupCI',
                name: 'Setup CI/CD',
                description: 'Setup continuous integration pipeline',
                category: 'automation',
                handler: () => this.automation.setupContinuousIntegration()
            },
            {
                id: 'ide.securityScan',
                name: 'Security Scan',
                description: 'Scan for security vulnerabilities',
                category: 'analysis',
                handler: (filePath) => this.errorDetection.detectSecurityVulnerabilities(filePath)
            },
            {
                id: 'ide.performanceAnalysis',
                name: 'Performance Analysis',
                description: 'Analyze code for performance issues',
                category: 'analysis',
                handler: (filePath) => this.errorDetection.detectPerformanceIssues(filePath)
            },
            {
                id: 'ide.generateReport',
                name: 'Generate Diagnostic Report',
                description: 'Generate comprehensive diagnostic report',
                category: 'analysis',
                handler: () => this.errorDetection.generateErrorReport()
            },
            {
                id: 'ide.runTests',
                name: 'Run Tests',
                description: 'Execute project tests',
                category: 'automation',
                handler: (options) => this.testingService.runTests(options)
            },
            {
                id: 'ide.generateTests',
                name: 'Generate Tests',
                description: 'Generate test files for code',
                category: 'automation',
                handler: (request) => this.testingService.generateTests(request)
            },
            {
                id: 'ide.gitStatus',
                name: 'Git Status',
                description: 'Get git repository status',
                category: 'automation',
                handler: () => this.gitService.getStatus()
            },
            {
                id: 'ide.gitCommit',
                name: 'Git Commit',
                description: 'Commit changes to git',
                category: 'automation',
                handler: (message, files) => this.gitService.commit(message, files)
            },
            {
                id: 'ide.runScript',
                name: 'Run Script',
                description: 'Execute npm/yarn script',
                category: 'automation',
                handler: (scriptName) => this.terminalService.runScript(scriptName)
            },
            {
                id: 'ide.installDependency',
                name: 'Install Dependency',
                description: 'Install npm package',
                category: 'automation',
                handler: (packageName, options) => this.terminalService.addDependency(packageName, options)
            }
        ];
        commands.forEach(command => this.commands.set(command.id, command));
    }
    // Request handlers
    async handleCodeRequest(query, intent) {
        // Use intelligent search to find relevant code
        const searchResults = await this.intelligentSearch.search(query);
        if (searchResults.length > 0) {
            const topResult = searchResults[0];
            return `Found relevant code in ${topResult.file} at line ${topResult.line}:\n\n${topResult.content}`;
        }
        return "I couldn't find specific code matching your query. Could you provide more details?";
    }
    async handleDebugRequest(query, intent) {
        const diagnostics = await this.errorDetection.detectErrors();
        if (diagnostics.errors.length > 0) {
            const criticalErrors = diagnostics.errors.filter(e => e.severity === 'error');
            return `Found ${criticalErrors.length} critical errors. Here are the top issues:\n\n${criticalErrors.slice(0, 3).map(e => `- ${e.message} (${e.file}:${e.line})`).join('\n')}`;
        }
        return "No critical errors detected. Your code looks good!";
    }
    async handleRefactorRequest(query, intent) {
        const refactoringPlan = await this.refactoring.analyzeForRefactoring();
        if (refactoringPlan.operations.length > 0) {
            const topOps = refactoringPlan.operations.slice(0, 3);
            return `Found ${refactoringPlan.operations.length} refactoring opportunities:\n\n${topOps.map(op => `- ${op.description} (${op.impact} impact)`).join('\n')}`;
        }
        return "Your code is well-structured! No major refactoring opportunities found.";
    }
    async handleGenerateRequest(query, intent) {
        // Extract generation requirements from query
        const request = {
            type: 'component',
            name: 'GeneratedComponent',
            description: query,
            options: {},
            context: {
                projectType: this.workspace.type,
                framework: this.workspace.framework || 'react',
                language: this.workspace.language,
                conventions: {
                    naming: 'camelCase',
                    indentation: 'spaces',
                    quotes: 'single'
                },
                existingPatterns: [],
                dependencies: []
            }
        };
        const generated = await this.codeGeneration.generateCode(request);
        return `Generated ${generated.files.length} files:\n\n${generated.files.map(f => `- ${f.path}: ${f.description}`).join('\n')}\n\nInstructions:\n${generated.instructions.join('\n')}`;
    }
    async handleSearchRequest(query, intent) {
        const results = await this.intelligentSearch.search(query);
        if (results.length > 0) {
            return `Found ${results.length} results:\n\n${results.slice(0, 5).map(r => `- ${r.file}:${r.line} - ${r.content.substring(0, 100)}...`).join('\n')}`;
        }
        return "No results found for your search query.";
    }
    // Analyze workspace
    async analyzeWorkspace() {
        // Detect project type and framework
        const analysis = await this.codeAnalysis.analyzeProject();
        this.workspace.capabilities = [
            { name: 'Code Analysis', description: 'Comprehensive code quality analysis', category: 'analysis', enabled: true, priority: 1 },
            { name: 'Intelligent Search', description: 'Multi-modal search capabilities', category: 'search', enabled: true, priority: 2 },
            { name: 'Code Generation', description: 'Context-aware code generation', category: 'generation', enabled: true, priority: 3 },
            { name: 'Automated Refactoring', description: 'Intelligent refactoring suggestions', category: 'refactoring', enabled: true, priority: 4 },
            { name: 'Error Detection', description: 'Proactive error detection and resolution', category: 'analysis', enabled: true, priority: 5 },
            { name: 'Automation', description: 'Development workflow automation', category: 'automation', enabled: true, priority: 6 }
        ];
    }
    // Setup integrations
    async setupIntegrations() {
        // Setup VS Code command palette integration
        this.commands.forEach((command, id) => {
            vscode.commands.registerCommand(id, command.handler);
        });
        // Setup status bar
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = "🧠 Advanced IDE";
        statusBarItem.tooltip = "Advanced IDE capabilities active";
        statusBarItem.command = 'ide.analyzeProject';
        statusBarItem.show();
    }
    // Get service by name
    getService(serviceName) {
        return this.services.get(serviceName);
    }
    // Execute terminal command safely
    async executeTerminalCommand(command) {
        try {
            const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const execAsync = promisify(exec);
            // Security check - only allow safe commands
            const allowedCommands = ['npm', 'yarn', 'git', 'node', 'tsc', 'eslint', 'prettier', 'test'];
            const commandStart = command.split(' ')[0];
            if (!allowedCommands.includes(commandStart)) {
                throw new Error(`Command "${commandStart}" is not allowed for security reasons. Allowed commands: ${allowedCommands.join(', ')}`);
            }
            const { stdout, stderr } = await execAsync(command, {
                cwd: this.workspace.root,
                timeout: 30000 // 30 second timeout
            });
            let result = `Command executed: ${command}\n\n`;
            if (stdout)
                result += `Output:\n${stdout}\n`;
            if (stderr)
                result += `Warnings/Errors:\n${stderr}\n`;
            return result;
        }
        catch (error) {
            throw new Error(`Command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Get workspace info
    getWorkspace() {
        return { ...this.workspace };
    }
    // Cleanup
    dispose() {
        this.automation.dispose();
        this.terminalService.dispose();
        this.services.clear();
        this.commands.clear();
    }
}
exports.AdvancedIDEOrchestrator = AdvancedIDEOrchestrator;
//# sourceMappingURL=AdvancedIDEOrchestrator.js.map