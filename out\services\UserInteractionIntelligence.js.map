{"version": 3, "file": "UserInteractionIntelligence.js", "sourceRoot": "", "sources": ["../../src/services/UserInteractionIntelligence.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAuFjC,MAAa,2BAA2B;IAQtC;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC9D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxE,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAE/C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED,0CAA0C;IAC1C,KAAK,CAAC,gBAAgB,CAAC,KAAa;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzE,6BAA6B;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM;SACP,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,+BAA+B;IAC/B,KAAK,CAAC,gBAAgB,CAAC,MAAkB,EAAE,KAAa;QACtD,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC;YACxB,KAAK,MAAM;gBACT,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM;YACR,KAAK,OAAO;gBACV,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACxD,MAAM;YACR,KAAK,UAAU;gBACb,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM;YACR,KAAK,UAAU;gBACb,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM;YACR,KAAK,SAAS;gBACZ,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,QAAQ;gBACX,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACzD,MAAM;YACR;gBACE,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,qCAAqC;QACrC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3E,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,4BAA4B;IAC5B,KAAK,CAAC,uBAAuB;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC1F,CAAC;IAED,0BAA0B;IAC1B,qBAAqB,CAAC,WAAqC;QACzD,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,WAAW,EAAE,CAAC;QACnF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,sBAAsB;IACtB,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,QAAiD;QACtF,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAC/E,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;YACrD,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,8BAA8B;IACtB,oBAAoB;QAC1B,qBAAqB;QACrB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,EAAE;YACnD,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACpD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC9B,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;iBAC5C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC,KAAK,EAAE,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBACzE,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG;oBAC5B,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI;oBAC1B,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS;iBACrC,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC9B,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;iBAC7C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB;IACX,KAAK,CAAC,iBAAiB,CAAC,MAAkB,EAAE,KAAa;QAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;QAE7F,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,uBAAuB,UAAU,CAAC,KAAK,mDAAmD,CAAC;QACpG,CAAC;QAED,OAAO,gGAAgG,CAAC;IAC1G,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAkB,EAAE,KAAa;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,oJAAoJ,CAAC;QAC9J,CAAC;QAED,OAAO,8GAA8G,CAAC;IACxH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAkB,EAAE,KAAa;QACnE,OAAO,4IAA4I,CAAC;IACtJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAkB,EAAE,KAAa;QACnE,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;QAEtE,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,kBAAkB,UAAU,CAAC,KAAK,yDAAyD,CAAC;QACrG,CAAC;QAED,OAAO,+FAA+F,CAAC;IACzG,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAkB,EAAE,KAAa;QAClE,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAEtE,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YAC1C,OAAO,kBAAkB,aAAa,CAAC,KAAK,SAAS,KAAK,WAAW,CAAC;QACxE,CAAC;QAED,OAAO,iFAAiF,CAAC;IAC3F,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAkB,EAAE,KAAa;QACjE,OAAO,wEAAwE,CAAC;IAClF,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAkB,EAAE,KAAa;QAClE,OAAO,2EAA2E,CAAC;IACrF,CAAC;IAED,yBAAyB;IACjB,iBAAiB;QACvB,OAAO;YACL,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,WAAW,EAAE,SAAS;aACvB;YACD,eAAe,EAAE;gBACf,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,cAAc;gBAChC,kBAAkB,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;gBAChD,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,IAAI;aACpB;SACF,CAAC;IACJ,CAAC;IAEO,4BAA4B;QAClC,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YAChC,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAEO,eAAe;QACrB,oEAAoE;QACpE,OAAO;YACL,UAAU,EAAE,cAAc;YAC1B,gBAAgB,EAAE,cAAc;YAChC,eAAe,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;YAClD,aAAa,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;YAC/D,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,eAAe;QACrB,kEAAkE;IACpE,CAAC;CACF;AApOD,kEAoOC;AAED,+BAA+B;AAC/B,MAAM,gBAAgB;IACpB,KAAK,CAAC,QAAQ,CAAC,KAAa,EAAE,OAAoB;QAChD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAE3C,qEAAqE;QACrE,IAAI,QAAQ,GAA2B,MAAM,CAAC;QAC9C,IAAI,IAAI,GAAuB,UAAU,CAAC;QAE1C,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3G,QAAQ,GAAG,OAAO,CAAC;YACnB,IAAI,GAAG,SAAS,CAAC;QACnB,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5H,QAAQ,GAAG,UAAU,CAAC;YACtB,IAAI,GAAG,SAAS,CAAC;QACnB,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACvH,QAAQ,GAAG,UAAU,CAAC;YACtB,IAAI,GAAG,SAAS,CAAC;QACnB,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3H,QAAQ,GAAG,SAAS,CAAC;YACrB,IAAI,GAAG,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChF,QAAQ,GAAG,QAAQ,CAAC;YACpB,IAAI,GAAG,SAAS,CAAC;QACnB,CAAC;QAED,OAAO;YACL,IAAI;YACJ,QAAQ;YACR,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YACrC,OAAO;SACR,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,MAAM,QAAQ,GAAmB,EAAE,CAAC;QAEpC,uEAAuE;QACvE,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnG,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,8BAA8B;AAC9B,MAAM,yBAAyB;IAC7B,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,mBAAmB,CAAC,OAAoB,EAAE,MAA0B;QAClE,MAAM,WAAW,GAA0B,EAAE,CAAC;QAE9C,mCAAmC;QACnC,IAAI,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC;gBACf,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,mDAAmD;gBAChE,QAAQ,EAAE,QAAQ;gBAClB,cAAc,EAAE,GAAG;gBACnB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AAED,oBAAoB;AACpB,MAAM,gBAAgB;IACpB,KAAK,CAAC,oBAAoB,CAAC,MAAkB,EAAE,OAAoB;QACjE,uCAAuC;IACzC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAA4B,EAAE,QAAgB;QACpE,2BAA2B;IAC7B,CAAC;IAED,aAAa,CAAC,QAAgB,EAAE,WAAwB;QACtD,2CAA2C;QAC3C,IAAI,WAAW,CAAC,gBAAgB,KAAK,UAAU,EAAE,CAAC;YAChD,OAAO,QAAQ,GAAG,+DAA+D,CAAC;QACpF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF"}