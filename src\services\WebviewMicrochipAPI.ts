// Webview-safe version of MicrochipAPI that doesn't import VSCode modules

export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface ConversationContext {
  history: ChatMessage[];
  userPreferences: Record<string, unknown>;
  sessionData: Record<string, unknown>;
  activeFile?: string;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  workspaceRoot?: string;
}

export class WebviewMicrochipAPI {
  private apiKey: string = '';
  private baseUrl: string = 'http://localhost:3001';
  private conversationHistory: ChatMessage[] = [];

  constructor(apiKey?: string) {
    if (apiKey) {
      this.apiKey = apiKey;
    }
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  getApiKey(): string {
    return this.apiKey;
  }

  async sendMessage(message: string, context?: ConversationContext): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key not set'
      };
    }

    if (!message.trim()) {
      return {
        success: false,
        message: '',
        error: 'Message cannot be empty'
      };
    }

    try {
      // Add user message to conversation history
      const userMessage: ChatMessage = {
        id: this.generateId(),
        text: message,
        isBot: false,
        timestamp: new Date()
      };
      this.conversationHistory.push(userMessage);

      // Get response from API
      const response = await this.callAPI(message);

      // Add bot response to conversation history
      const botMessage: ChatMessage = {
        id: this.generateId(),
        text: response,
        isBot: true,
        timestamp: new Date()
      };
      this.conversationHistory.push(botMessage);

      // Keep conversation history manageable
      if (this.conversationHistory.length > 20) {
        this.conversationHistory = this.conversationHistory.slice(-20);
      }

      return {
        success: true,
        message: response
      };
    } catch (error) {
      console.error('Error sending message:', error);
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }

  clearConversationHistory(): void {
    this.conversationHistory = [];
  }

  private async callAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5).map(msg => msg.text),
      category: 101,
      logQnA: true,
      client: "webview-chatbot",
      apiKey: this.apiKey
    };

    try {
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      const data = await response.json() as {
        success?: boolean;
        message?: string;
        error?: string;
      };

      if (!response.ok) {
        if (data.error) {
          throw new Error(data.error);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      if (data.success && data.message) {
        return data.message;
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError' || error.name === 'TimeoutError') {
          throw new Error('Request timeout - Server took too long to respond');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - Please check if the backend server is running on port 3001');
        } else {
          throw error;
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }

  async testConnection(): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key is required'
      };
    }

    try {
      // Test connection by making a simple API call
      const response = await this.callAPI('Hello, are you working?');
      return {
        success: true,
        message: response
      };
    } catch (error) {
      console.error('Connection test failed:', error);
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

// Export a singleton instance for use in webview
export const webviewMicrochipAPI = new WebviewMicrochipAPI();
