"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedAgentTools = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const glob_1 = require("glob");
const vscode = __importStar(require("vscode"));
class AdvancedAgentTools {
    constructor(workspaceRoot) {
        this.projectIntelligence = null;
        this.codeContext = null;
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.fileSystemContext = {
            workspaceRoot: this.workspaceRoot,
            recentFiles: []
        };
        this.initializeProjectIntelligence();
    }
    // Initialize project intelligence by analyzing the codebase
    async initializeProjectIntelligence() {
        try {
            this.projectIntelligence = await this.analyzeProjectStructure();
            this.codeContext = await this.detectCodeContext();
        }
        catch (error) {
            console.error('Failed to initialize project intelligence:', error);
        }
    }
    // Analyze project structure and patterns
    async analyzeProjectStructure() {
        const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
        let techStack = [];
        let architecture = 'unknown';
        // Analyze package.json if it exists
        if (await fs.pathExists(packageJsonPath)) {
            try {
                const packageJson = await fs.readJson(packageJsonPath);
                const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
                // Detect tech stack
                if (dependencies.react)
                    techStack.push('React');
                if (dependencies.vue)
                    techStack.push('Vue');
                if (dependencies.angular)
                    techStack.push('Angular');
                if (dependencies.express)
                    techStack.push('Express');
                if (dependencies.typescript)
                    techStack.push('TypeScript');
                if (dependencies.langchain)
                    techStack.push('LangChain');
                if (dependencies.vscode)
                    techStack.push('VS Code Extension');
                // Detect architecture patterns
                if (dependencies.react && dependencies.vscode) {
                    architecture = 'VS Code Extension with React Webview';
                }
                else if (dependencies.express) {
                    architecture = 'Node.js Backend';
                }
                else if (dependencies.react) {
                    architecture = 'React Application';
                }
            }
            catch (error) {
                console.error('Error analyzing package.json:', error);
            }
        }
        // Detect patterns from file structure
        const patterns = await this.detectProjectPatterns();
        return {
            architecture,
            techStack,
            patterns,
            codeContext: await this.detectCodeContext(),
            qualityMetrics: {
                complexity: 0,
                maintainability: 0,
                testCoverage: 0
            }
        };
    }
    // Detect code context and conventions
    async detectCodeContext() {
        const tsConfigPath = path.join(this.workspaceRoot, 'tsconfig.json');
        const eslintConfigPath = path.join(this.workspaceRoot, 'eslint.config.js');
        let language = 'javascript';
        let framework;
        let conventions = {
            naming: 'camelCase',
            indentation: 'spaces',
            indentSize: 2,
            quotes: 'single'
        };
        // Check for TypeScript
        if (await fs.pathExists(tsConfigPath)) {
            language = 'typescript';
        }
        // Analyze existing files for conventions
        const sampleFiles = await this.getSampleFiles();
        if (sampleFiles.length > 0) {
            conventions = await this.analyzeCodeConventions(sampleFiles);
        }
        return {
            language,
            framework,
            patterns: await this.detectProjectPatterns(),
            conventions
        };
    }
    // Enhanced File System Tools with Intelligence
    createIntelligentReadFileTool() {
        return {
            name: 'IntelligentReadFile',
            description: 'Intelligently reads and analyzes file content with context awareness and pattern recognition',
            keywords: ['read', 'file', 'content', 'show', 'display', 'view', 'open', 'analyze'],
            execute: async (query, context) => {
                try {
                    const filePath = this.extractFilePathFromQuery(query);
                    const fullPath = path.resolve(this.workspaceRoot, filePath);
                    if (!await fs.pathExists(fullPath)) {
                        // Suggest similar files if exact match not found
                        const suggestions = await this.findSimilarFiles(filePath);
                        return `Error: File '${filePath}' does not exist.\n${suggestions.length > 0 ? `\nDid you mean:\n${suggestions.map(s => `- ${s}`).join('\n')}` : ''}`;
                    }
                    const content = await fs.readFile(fullPath, 'utf-8');
                    this.updateRecentFiles(filePath);
                    // Analyze file context
                    const analysis = await this.analyzeFileContext(filePath, content);
                    return `File: ${filePath}\n${analysis.summary}\n\`\`\`${analysis.language}\n${content}\n\`\`\`\n\n${analysis.insights}`;
                }
                catch (error) {
                    return `Error reading file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createIntelligentWriteFileTool() {
        return {
            name: 'IntelligentWriteFile',
            description: 'Intelligently writes content to files with automatic formatting, pattern matching, and dependency management',
            keywords: ['write', 'save', 'create', 'update', 'modify', 'edit', 'generate'],
            execute: async (query, context) => {
                try {
                    const operation = await this.parseIntelligentWriteOperation(query);
                    // Validate operation
                    const validation = await this.validateFileOperation(operation);
                    if (!validation.isValid) {
                        return `Error: ${validation.reason}`;
                    }
                    // Apply intelligent formatting
                    const formattedContent = await this.applyIntelligentFormatting(operation.content, operation.targetPath);
                    // Create directory if needed
                    const dir = path.dirname(path.resolve(this.workspaceRoot, operation.targetPath));
                    await fs.ensureDir(dir);
                    // Write file
                    await fs.writeFile(path.resolve(this.workspaceRoot, operation.targetPath), formattedContent);
                    // Update dependencies if needed
                    await this.updateDependencies(operation);
                    this.updateRecentFiles(operation.targetPath);
                    return `✅ Successfully ${operation.operation}d file: ${operation.targetPath}\n${operation.reasoning}\n\nImpact: ${operation.impact}\nDependencies updated: ${operation.dependencies.length > 0 ? operation.dependencies.join(', ') : 'None'}`;
                }
                catch (error) {
                    return `Error writing file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Helper methods for intelligent operations
    async findSimilarFiles(targetPath) {
        try {
            const allFiles = await (0, glob_1.glob)('**/*', {
                cwd: this.workspaceRoot,
                ignore: ['node_modules/**', '.git/**', 'dist/**', 'out/**'],
                nodir: true
            });
            const targetName = path.basename(targetPath).toLowerCase();
            const targetDir = path.dirname(targetPath).toLowerCase();
            return allFiles
                .filter(file => {
                const fileName = path.basename(file).toLowerCase();
                const fileDir = path.dirname(file).toLowerCase();
                // Check for similar names or directories
                return fileName.includes(targetName.split('.')[0]) ||
                    fileDir.includes(targetDir) ||
                    this.calculateSimilarity(fileName, targetName) > 0.6;
            })
                .slice(0, 5); // Limit to 5 suggestions
        }
        catch (error) {
            return [];
        }
    }
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        if (longer.length === 0)
            return 1.0;
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++)
            matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++)
            matrix[j][0] = j;
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, matrix[j - 1][i] + 1, matrix[j - 1][i - 1] + indicator);
            }
        }
        return matrix[str2.length][str1.length];
    }
    async analyzeFileContext(filePath, content) {
        const ext = path.extname(filePath);
        const language = this.getLanguageFromExtension(ext);
        // Analyze content structure
        const lines = content.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        let insights = '';
        // Language-specific analysis
        if (language === 'typescript' || language === 'javascript') {
            const imports = lines.filter(line => line.trim().startsWith('import'));
            const exports = lines.filter(line => line.includes('export'));
            const functions = lines.filter(line => /function|const.*=.*=>|class/.test(line));
            insights = `📊 Analysis:\n- ${imports.length} imports\n- ${exports.length} exports\n- ${functions.length} functions/classes\n- ${nonEmptyLines.length} lines of code`;
        }
        return {
            summary: `📁 ${path.basename(filePath)} (${language}) - ${nonEmptyLines.length} lines`,
            language,
            insights
        };
    }
    getLanguageFromExtension(ext) {
        const languageMap = {
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown'
        };
        return languageMap[ext] || 'text';
    }
    createReadFileTool() {
        return {
            name: 'ReadFile',
            description: 'Reads the content of a file',
            keywords: ['read', 'open', 'view', 'show', 'display', 'content'],
            execute: async (query, context) => {
                try {
                    const filePath = this.extractFilePathFromQuery(query);
                    const fullPath = path.resolve(this.workspaceRoot, filePath);
                    if (!await fs.pathExists(fullPath)) {
                        return `File '${filePath}' does not exist`;
                    }
                    const content = await fs.readFile(fullPath, 'utf-8');
                    this.updateRecentFiles(filePath);
                    return `Content of '${filePath}':\n\n${content}`;
                }
                catch (error) {
                    return `Error reading file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createWriteFileTool() {
        return {
            name: 'WriteFile',
            description: 'Writes content to a file. Creates the file if it doesn\'t exist, overwrites if it does',
            keywords: ['write', 'save', 'create', 'update', 'modify', 'edit'],
            execute: async (query, context) => {
                try {
                    const { filePath, content } = this.extractFilePathAndContentFromQuery(query);
                    const fullPath = path.resolve(this.workspaceRoot, filePath);
                    // Ensure directory exists
                    await fs.ensureDir(path.dirname(fullPath));
                    await fs.writeFile(fullPath, content, 'utf-8');
                    this.updateRecentFiles(filePath);
                    return `Successfully wrote content to '${filePath}'`;
                }
                catch (error) {
                    return `Error writing file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createCreateFileTool() {
        return {
            name: 'CreateFile',
            description: 'Creates a new file with initial content',
            keywords: ['create', 'new', 'file', 'generate', 'make'],
            execute: async (query, context) => {
                try {
                    const { filePath, content } = this.extractFilePathAndContentFromQuery(query);
                    const fullPath = path.resolve(this.workspaceRoot, filePath);
                    if (await fs.pathExists(fullPath)) {
                        return `Error: File '${filePath}' already exists. Use WriteFile to overwrite.`;
                    }
                    // Ensure directory exists
                    await fs.ensureDir(path.dirname(fullPath));
                    await fs.writeFile(fullPath, content || '', 'utf-8');
                    this.updateRecentFiles(filePath);
                    return `Successfully created file '${filePath}'`;
                }
                catch (error) {
                    return `Error creating file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createMoveFileTool() {
        return {
            name: 'MoveFile',
            description: 'Moves or renames a file from source to destination path',
            keywords: ['move', 'rename', 'relocate', 'transfer'],
            execute: async (query, context) => {
                try {
                    const { sourcePath, destPath } = this.extractSourceAndDestFromQuery(query);
                    const fullSourcePath = path.resolve(this.workspaceRoot, sourcePath);
                    const fullDestPath = path.resolve(this.workspaceRoot, destPath);
                    if (!await fs.pathExists(fullSourcePath)) {
                        return `Error: Source file '${sourcePath}' does not exist.`;
                    }
                    // Ensure destination directory exists
                    await fs.ensureDir(path.dirname(fullDestPath));
                    await fs.move(fullSourcePath, fullDestPath);
                    this.updateRecentFiles(destPath);
                    return `Successfully moved '${sourcePath}' to '${destPath}'`;
                }
                catch (error) {
                    return `Error moving file: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createListFilesTool() {
        return {
            name: 'ListFiles',
            description: 'Lists all files in the project directory with optional pattern filtering',
            keywords: ['list', 'files', 'directory', 'structure', 'find', 'search'],
            execute: async (query, context) => {
                try {
                    const pattern = this.extractPatternFromQuery(query) || '**/*';
                    const options = {
                        cwd: this.workspaceRoot,
                        ignore: ['node_modules/**', '.git/**', 'dist/**', 'out/**', '*.log']
                    };
                    const files = await (0, glob_1.glob)(pattern, options);
                    if (files.length === 0) {
                        return `No files found matching pattern '${pattern}'`;
                    }
                    const fileList = files.slice(0, 100).join('\n'); // Limit to first 100 files
                    const truncated = files.length > 100 ? `\n... and ${files.length - 100} more files` : '';
                    return `Files in workspace (${files.length} total):\n${fileList}${truncated}`;
                }
                catch (error) {
                    return `Error listing files: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createSearchCodeTool() {
        return {
            name: 'SearchCode',
            description: 'Searches for code patterns, functions, classes, or text across the entire codebase',
            keywords: ['search', 'find', 'grep', 'function', 'class', 'variable', 'pattern'],
            execute: async (query, context) => {
                try {
                    const searchTerm = this.extractSearchTermFromQuery(query);
                    const filePattern = '**/*.{ts,tsx,js,jsx,py,java,c,cpp,h,hpp}';
                    const files = await (0, glob_1.glob)(filePattern, {
                        cwd: this.workspaceRoot,
                        ignore: ['node_modules/**', '.git/**', 'dist/**', 'out/**']
                    });
                    const results = [];
                    for (const file of files.slice(0, 50)) { // Limit search to 50 files
                        const fullPath = path.resolve(this.workspaceRoot, file);
                        const content = await fs.readFile(fullPath, 'utf-8');
                        const lines = content.split('\n');
                        lines.forEach((line, index) => {
                            if (line.toLowerCase().includes(searchTerm.toLowerCase())) {
                                results.push(`${file}:${index + 1}: ${line.trim()}`);
                            }
                        });
                    }
                    if (results.length === 0) {
                        return `No matches found for '${searchTerm}'`;
                    }
                    const limitedResults = results.slice(0, 20);
                    const truncated = results.length > 20 ? `\n... and ${results.length - 20} more matches` : '';
                    return `Search results for '${searchTerm}' (${results.length} matches):\n${limitedResults.join('\n')}${truncated}`;
                }
                catch (error) {
                    return `Error searching code: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Context Management Tools
    createGetActiveFileContextTool() {
        return {
            name: 'GetActiveFileContext',
            description: 'Returns information about the currently active file, cursor position, and selected text',
            keywords: ['active', 'current', 'file', 'context', 'cursor', 'selection'],
            execute: async (query, context) => {
                try {
                    const activeEditor = vscode.window.activeTextEditor;
                    if (!activeEditor) {
                        return 'No active file in the editor';
                    }
                    const document = activeEditor.document;
                    const selection = activeEditor.selection;
                    const position = activeEditor.selection.active;
                    const relativePath = vscode.workspace.asRelativePath(document.uri);
                    const selectedText = document.getText(selection);
                    let result = `Active File: ${relativePath}\n`;
                    result += `Language: ${document.languageId}\n`;
                    result += `Cursor Position: Line ${position.line + 1}, Column ${position.character + 1}\n`;
                    if (selectedText) {
                        result += `Selected Text:\n\`\`\`\n${selectedText}\n\`\`\`\n`;
                    }
                    // Update context
                    context.activeFile = relativePath;
                    context.selectedText = selectedText;
                    context.cursorPosition = { line: position.line, character: position.character };
                    return result;
                }
                catch (error) {
                    return `Error getting active file context: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Helper methods
    extractFilePathFromQuery(query) {
        // Extract file path from various query formats
        const pathMatch = query.match(/(?:file|path|read)\s+["']?([^"'\s]+)["']?/i);
        return pathMatch ? pathMatch[1] : query.trim();
    }
    extractFilePathAndContentFromQuery(query) {
        // Try to extract file path and content from query
        const match = query.match(/(?:file|path)\s+["']?([^"'\s]+)["']?\s+(?:with\s+)?content\s*[:=]?\s*([\s\S]*)/i);
        if (match) {
            return { filePath: match[1], content: match[2].trim() };
        }
        // Fallback: assume first word is file path, rest is content
        const parts = query.split(/\s+/);
        return { filePath: parts[0], content: parts.slice(1).join(' ') };
    }
    extractSourceAndDestFromQuery(query) {
        const match = query.match(/(?:move|rename)\s+["']?([^"'\s]+)["']?\s+(?:to\s+)?["']?([^"'\s]+)["']?/i);
        if (match) {
            return { sourcePath: match[1], destPath: match[2] };
        }
        const parts = query.split(/\s+/);
        return { sourcePath: parts[0], destPath: parts[1] || parts[0] };
    }
    extractPatternFromQuery(query) {
        const match = query.match(/(?:pattern|filter)\s+["']?([^"'\s]+)["']?/i);
        return match ? match[1] : null;
    }
    extractSearchTermFromQuery(query) {
        const match = query.match(/(?:search|find)\s+(?:for\s+)?["']?([^"']+)["']?/i);
        return match ? match[1].trim() : query.trim();
    }
    updateRecentFiles(filePath) {
        this.fileSystemContext.recentFiles = [
            filePath,
            ...this.fileSystemContext.recentFiles.filter(f => f !== filePath)
        ].slice(0, 10);
    }
    // Code Refactoring Tools
    createExtractFunctionTool() {
        return {
            name: 'ExtractFunction',
            description: 'Extracts selected code into a new function with proper parameters and return values',
            keywords: ['extract', 'function', 'refactor', 'method', 'separate'],
            execute: async (query, context) => {
                try {
                    const { code, functionName, filePath } = this.extractRefactorParams(query);
                    if (!code) {
                        return 'Error: No code provided to extract into function';
                    }
                    const extractedFunction = this.generateExtractedFunction(code, functionName);
                    const updatedCode = this.replaceCodeWithFunctionCall(code, functionName);
                    return `Extracted function:\n\`\`\`typescript\n${extractedFunction}\n\`\`\`\n\nReplacement call:\n\`\`\`typescript\n${updatedCode}\n\`\`\``;
                }
                catch (error) {
                    return `Error extracting function: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createMoveCodeBetweenFilesTool() {
        return {
            name: 'MoveCodeBetweenFiles',
            description: 'Moves functions, classes, or code blocks from one file to another with proper imports',
            keywords: ['move', 'code', 'function', 'class', 'file', 'transfer', 'relocate'],
            execute: async (query, context) => {
                try {
                    const { sourceFile, targetFile, codeToMove, codeType } = this.extractMoveParams(query);
                    // Read source file
                    const sourceContent = await fs.readFile(path.resolve(this.workspaceRoot, sourceFile), 'utf-8');
                    // Extract the code to move
                    const { extractedCode, remainingCode } = this.extractCodeFromFile(sourceContent, codeToMove, codeType);
                    // Update source file
                    await fs.writeFile(path.resolve(this.workspaceRoot, sourceFile), remainingCode, 'utf-8');
                    // Read or create target file
                    let targetContent = '';
                    const targetPath = path.resolve(this.workspaceRoot, targetFile);
                    if (await fs.pathExists(targetPath)) {
                        targetContent = await fs.readFile(targetPath, 'utf-8');
                    }
                    // Add code to target file with proper imports
                    const updatedTargetContent = this.addCodeToFile(targetContent, extractedCode, sourceFile, targetFile);
                    await fs.writeFile(targetPath, updatedTargetContent, 'utf-8');
                    // Update imports in source file if needed
                    const updatedSourceContent = this.updateImportsInFile(remainingCode, codeToMove, targetFile);
                    await fs.writeFile(path.resolve(this.workspaceRoot, sourceFile), updatedSourceContent, 'utf-8');
                    return `Successfully moved ${codeType} '${codeToMove}' from ${sourceFile} to ${targetFile}`;
                }
                catch (error) {
                    return `Error moving code: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createGenerateCodeTool() {
        return {
            name: 'GenerateCode',
            description: 'Generates code based on specifications (functions, classes, interfaces, tests)',
            keywords: ['generate', 'create', 'code', 'function', 'class', 'interface', 'test'],
            execute: async (query, context) => {
                try {
                    const { codeType, specification, language, filePath } = this.extractGenerationParams(query);
                    const generatedCode = this.generateCodeFromSpec(codeType, specification, language);
                    if (filePath) {
                        // Save to file if path specified
                        const fullPath = path.resolve(this.workspaceRoot, filePath);
                        await fs.ensureDir(path.dirname(fullPath));
                        let existingContent = '';
                        if (await fs.pathExists(fullPath)) {
                            existingContent = await fs.readFile(fullPath, 'utf-8');
                        }
                        const updatedContent = this.insertCodeIntoFile(existingContent, generatedCode, codeType);
                        await fs.writeFile(fullPath, updatedContent, 'utf-8');
                        return `Generated ${codeType} and saved to ${filePath}:\n\`\`\`${language}\n${generatedCode}\n\`\`\``;
                    }
                    else {
                        return `Generated ${codeType}:\n\`\`\`${language}\n${generatedCode}\n\`\`\``;
                    }
                }
                catch (error) {
                    return `Error generating code: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    createRefactorCodeTool() {
        return {
            name: 'RefactorCode',
            description: 'Refactors existing code to improve structure, readability, and maintainability',
            keywords: ['refactor', 'improve', 'optimize', 'restructure', 'clean'],
            execute: async (query, context) => {
                try {
                    const { filePath, refactorType, targetCode } = this.extractRefactorParams(query);
                    if (!filePath) {
                        return 'Error: File path required for refactoring';
                    }
                    const content = await fs.readFile(path.resolve(this.workspaceRoot, filePath), 'utf-8');
                    const refactoredCode = this.performRefactoring(content, refactorType, targetCode);
                    await fs.writeFile(path.resolve(this.workspaceRoot, filePath), refactoredCode, 'utf-8');
                    return `Successfully refactored ${filePath} using ${refactorType} refactoring`;
                }
                catch (error) {
                    return `Error refactoring code: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Helper methods for refactoring
    extractRefactorParams(query) {
        // Extract parameters for refactoring operations
        const codeMatch = query.match(/code\s*[:=]\s*```[\s\S]*?```/i);
        const functionNameMatch = query.match(/(?:function|name)\s*[:=]\s*(\w+)/i);
        const filePathMatch = query.match(/(?:file|path)\s*[:=]\s*([^\s]+)/i);
        return {
            code: codeMatch ? codeMatch[0].replace(/```[\w]*\n?|\n?```/g, '') : '',
            functionName: functionNameMatch ? functionNameMatch[1] : 'extractedFunction',
            filePath: filePathMatch ? filePathMatch[1] : '',
            refactorType: this.detectRefactorType(query)
        };
    }
    extractMoveParams(query) {
        const sourceMatch = query.match(/(?:from|source)\s+([^\s]+)/i);
        const targetMatch = query.match(/(?:to|target)\s+([^\s]+)/i);
        const codeMatch = query.match(/(?:move|code)\s+(\w+)/i);
        const typeMatch = query.match(/(?:function|class|interface|method)\s+(\w+)/i);
        return {
            sourceFile: sourceMatch ? sourceMatch[1] : '',
            targetFile: targetMatch ? targetMatch[1] : '',
            codeToMove: codeMatch ? codeMatch[1] : (typeMatch ? typeMatch[1] : ''),
            codeType: this.detectCodeType(query)
        };
    }
    extractGenerationParams(query) {
        const typeMatch = query.match(/(?:generate|create)\s+(function|class|interface|test)/i);
        const specMatch = query.match(/(?:spec|specification)\s*[:=]\s*(.+)/i);
        const langMatch = query.match(/(?:language|lang)\s*[:=]\s*(\w+)/i);
        const fileMatch = query.match(/(?:file|path)\s*[:=]\s*([^\s]+)/i);
        return {
            codeType: typeMatch ? typeMatch[1] : 'function',
            specification: specMatch ? specMatch[1] : query,
            language: langMatch ? langMatch[1] : 'typescript',
            filePath: fileMatch ? fileMatch[1] : null
        };
    }
    generateExtractedFunction(code, functionName) {
        // Analyze code to determine parameters and return type
        const variables = this.extractVariables(code);
        const returnValue = this.detectReturnValue(code);
        let params = variables.length > 0 ? variables.join(': any, ') + ': any' : '';
        let returnType = returnValue ? ': any' : ': void';
        return `function ${functionName}(${params})${returnType} {\n  ${code.split('\n').join('\n  ')}\n}`;
    }
    replaceCodeWithFunctionCall(code, functionName) {
        const variables = this.extractVariables(code);
        const params = variables.join(', ');
        return `${functionName}(${params});`;
    }
    extractVariables(code) {
        // Simple variable extraction - can be enhanced
        const varMatches = code.match(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g) || [];
        return [...new Set(varMatches)].filter(v => !['const', 'let', 'var', 'function', 'return'].includes(v));
    }
    detectReturnValue(code) {
        return /return\s+/.test(code);
    }
    detectRefactorType(query) {
        if (/extract.*function/i.test(query))
            return 'extract-function';
        if (/rename/i.test(query))
            return 'rename';
        if (/inline/i.test(query))
            return 'inline';
        if (/move/i.test(query))
            return 'move';
        return 'general';
    }
    detectCodeType(query) {
        if (/function/i.test(query))
            return 'function';
        if (/class/i.test(query))
            return 'class';
        if (/interface/i.test(query))
            return 'interface';
        if (/method/i.test(query))
            return 'method';
        return 'code';
    }
    extractCodeFromFile(content, codeToMove, codeType) {
        // Simple extraction - can be enhanced with proper AST parsing
        const lines = content.split('\n');
        let startIndex = -1;
        let endIndex = -1;
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(codeToMove)) {
                startIndex = i;
                // Find the end of the code block
                let braceCount = 0;
                for (let j = i; j < lines.length; j++) {
                    if (lines[j].includes('{'))
                        braceCount++;
                    if (lines[j].includes('}'))
                        braceCount--;
                    if (braceCount === 0 && j > i) {
                        endIndex = j;
                        break;
                    }
                }
                break;
            }
        }
        if (startIndex === -1) {
            throw new Error(`Code '${codeToMove}' not found in file`);
        }
        const extractedCode = lines.slice(startIndex, endIndex + 1).join('\n');
        const remainingCode = [
            ...lines.slice(0, startIndex),
            ...lines.slice(endIndex + 1)
        ].join('\n');
        return { extractedCode, remainingCode };
    }
    addCodeToFile(targetContent, extractedCode, sourceFile, targetFile) {
        // Add imports if needed
        const imports = this.generateImports(sourceFile, targetFile);
        if (targetContent.trim() === '') {
            return imports + '\n\n' + extractedCode;
        }
        return targetContent + '\n\n' + extractedCode;
    }
    updateImportsInFile(content, movedCode, targetFile) {
        // Add import for moved code
        const importStatement = this.generateImportStatement(movedCode, targetFile);
        if (content.includes('import')) {
            // Add to existing imports
            const lines = content.split('\n');
            const lastImportIndex = lines.map((line, index) => line.trim().startsWith('import') ? index : -1)
                .filter(index => index !== -1)
                .pop() ?? -1;
            lines.splice(lastImportIndex + 1, 0, importStatement);
            return lines.join('\n');
        }
        else {
            // Add as first import
            return importStatement + '\n\n' + content;
        }
    }
    generateImports(sourceFile, targetFile) {
        // Generate relative import path
        const relativePath = path.relative(path.dirname(targetFile), sourceFile).replace(/\\/g, '/');
        return `// Imports from ${sourceFile}`;
    }
    generateImportStatement(movedCode, targetFile) {
        const fileName = path.basename(targetFile, path.extname(targetFile));
        return `import { ${movedCode} } from './${fileName}';`;
    }
    generateCodeFromSpec(codeType, specification, language) {
        // Basic code generation - can be enhanced with AI
        switch (codeType) {
            case 'function':
                return this.generateFunction(specification, language);
            case 'class':
                return this.generateClass(specification, language);
            case 'interface':
                return this.generateInterface(specification, language);
            case 'test':
                return this.generateTest(specification, language);
            default:
                return `// Generated ${codeType}\n// ${specification}`;
        }
    }
    generateFunction(spec, language) {
        const functionName = this.extractFunctionName(spec) || 'generatedFunction';
        return `function ${functionName}() {\n  // TODO: Implement ${spec}\n}`;
    }
    generateClass(spec, language) {
        const className = this.extractClassName(spec) || 'GeneratedClass';
        return `class ${className} {\n  constructor() {\n    // TODO: Implement ${spec}\n  }\n}`;
    }
    generateInterface(spec, language) {
        const interfaceName = this.extractInterfaceName(spec) || 'GeneratedInterface';
        return `interface ${interfaceName} {\n  // TODO: Define properties for ${spec}\n}`;
    }
    generateTest(spec, language) {
        const testName = this.extractTestName(spec) || 'generated test';
        return `describe('${testName}', () => {\n  it('should ${spec}', () => {\n    // TODO: Implement test\n  });\n});`;
    }
    extractFunctionName(spec) {
        const match = spec.match(/function\s+(\w+)/i) || spec.match(/(\w+)\s+function/i);
        return match ? match[1] : null;
    }
    extractClassName(spec) {
        const match = spec.match(/class\s+(\w+)/i) || spec.match(/(\w+)\s+class/i);
        return match ? match[1] : null;
    }
    extractInterfaceName(spec) {
        const match = spec.match(/interface\s+(\w+)/i) || spec.match(/(\w+)\s+interface/i);
        return match ? match[1] : null;
    }
    extractTestName(spec) {
        const match = spec.match(/test\s+(.+)/i) || spec.match(/(.+)\s+test/i);
        return match ? match[1] : null;
    }
    insertCodeIntoFile(existingContent, generatedCode, codeType) {
        if (existingContent.trim() === '') {
            return generatedCode;
        }
        // Insert at appropriate location based on code type
        return existingContent + '\n\n' + generatedCode;
    }
    performRefactoring(content, refactorType, targetCode) {
        // Basic refactoring implementations
        switch (refactorType) {
            case 'rename':
                return this.performRename(content, targetCode);
            case 'extract-function':
                return this.performExtractFunction(content, targetCode);
            default:
                return content; // No changes for unknown refactor types
        }
    }
    performRename(content, targetCode) {
        // Simple rename implementation
        const [oldName, newName] = targetCode.split(' to ');
        return content.replace(new RegExp(`\\b${oldName}\\b`, 'g'), newName);
    }
    performExtractFunction(content, targetCode) {
        // Extract function implementation
        return content; // Placeholder
    }
    // Get all tools as a collection
    createPrimeNumberProgramTool() {
        return {
            name: 'GeneratePrimeNumberProgram',
            description: 'Automatically generates a complete prime number program and creates it as a file in the workspace',
            keywords: ['prime', 'number', 'program', 'generate', 'create', 'algorithm', 'sieve', 'check'],
            execute: async (query, context) => {
                try {
                    // Detect programming language preference from query or workspace
                    const language = await this.detectLanguagePreference(query);
                    const fileName = this.generatePrimeNumberFileName(language);
                    const fullPath = path.resolve(this.workspaceRoot, fileName);
                    // Generate the prime number program code
                    const code = this.generatePrimeNumberCode(language, query);
                    // Create the file
                    await fs.ensureDir(path.dirname(fullPath));
                    await fs.writeFile(fullPath, code, 'utf-8');
                    // Update recent files
                    this.updateRecentFiles(fileName);
                    // Try to open the file in VSCode if available
                    try {
                        if (typeof vscode !== 'undefined' && vscode.workspace) {
                            const uri = vscode.Uri.file(fullPath);
                            const document = await vscode.workspace.openTextDocument(uri);
                            await vscode.window.showTextDocument(document);
                        }
                    }
                    catch (vscodeError) {
                        // VSCode not available, file still created successfully
                    }
                    return `✅ Prime number program created successfully!

📁 File: ${fileName}
🔧 Language: ${language}
📍 Location: ${fullPath}

The program includes:
- Prime number checking function
- Sieve of Eratosthenes algorithm
- Interactive user input
- Comprehensive examples and documentation

${typeof vscode !== 'undefined' && vscode.workspace ?
                        '🎯 File has been opened in your editor!' :
                        '💡 You can now open the file in your editor to view and run the code.'}`;
                }
                catch (error) {
                    return `Error creating prime number program: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    async detectLanguagePreference(query) {
        const queryLower = query.toLowerCase();
        // Check for explicit language mentions
        if (queryLower.includes('python') || queryLower.includes('.py'))
            return 'python';
        if (queryLower.includes('javascript') || queryLower.includes('js') || queryLower.includes('.js'))
            return 'javascript';
        if (queryLower.includes('typescript') || queryLower.includes('ts') || queryLower.includes('.ts'))
            return 'typescript';
        if (queryLower.includes('java') || queryLower.includes('.java'))
            return 'java';
        if (queryLower.includes('c++') || queryLower.includes('cpp') || queryLower.includes('.cpp'))
            return 'cpp';
        if (queryLower.includes('c#') || queryLower.includes('csharp') || queryLower.includes('.cs'))
            return 'csharp';
        if (queryLower.includes('go') || queryLower.includes('golang') || queryLower.includes('.go'))
            return 'go';
        if (queryLower.includes('rust') || queryLower.includes('.rs'))
            return 'rust';
        // Check workspace for existing files to infer preference
        try {
            const files = await (0, glob_1.glob)('**/*.{py,js,ts,java,cpp,cs,go,rs}', {
                cwd: this.workspaceRoot,
                ignore: ['node_modules/**', '.git/**']
            });
            const extensions = files.map(f => path.extname(f).toLowerCase());
            const counts = {};
            extensions.forEach(ext => {
                counts[ext] = (counts[ext] || 0) + 1;
            });
            const mostCommon = Object.entries(counts).sort(([, a], [, b]) => b - a)[0];
            if (mostCommon) {
                const extMap = {
                    '.py': 'python',
                    '.js': 'javascript',
                    '.ts': 'typescript',
                    '.java': 'java',
                    '.cpp': 'cpp',
                    '.cs': 'csharp',
                    '.go': 'go',
                    '.rs': 'rust'
                };
                return extMap[mostCommon[0]] || 'python';
            }
        }
        catch (error) {
            // Ignore errors in file detection
        }
        // Default to Python as it's beginner-friendly
        return 'python';
    }
    generatePrimeNumberFileName(language) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const extensions = {
            'python': 'py',
            'javascript': 'js',
            'typescript': 'ts',
            'java': 'java',
            'cpp': 'cpp',
            'csharp': 'cs',
            'go': 'go',
            'rust': 'rs'
        };
        const ext = extensions[language] || 'py';
        return `prime_numbers_${timestamp}.${ext}`;
    }
    generatePrimeNumberCode(language, query) {
        const templates = {
            'python': `#!/usr/bin/env python3
"""
Prime Number Generator and Checker
Generated by Microchip AI Assistant

This program provides comprehensive prime number functionality including:
- Prime number checking
- Sieve of Eratosthenes algorithm
- Prime number generation
- Interactive user interface
"""

import math
import time
from typing import List, Set

def is_prime(n: int) -> bool:
    """
    Check if a number is prime using optimized trial division.

    Args:
        n: The number to check

    Returns:
        bool: True if the number is prime, False otherwise
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False

    # Check odd divisors up to sqrt(n)
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    return True

def sieve_of_eratosthenes(limit: int) -> List[int]:
    """
    Generate all prime numbers up to a given limit using the Sieve of Eratosthenes.

    Args:
        limit: The upper limit for prime generation

    Returns:
        List[int]: List of all prime numbers up to the limit
    """
    if limit < 2:
        return []

    # Initialize sieve
    sieve = [True] * (limit + 1)
    sieve[0] = sieve[1] = False

    # Apply sieve algorithm
    for i in range(2, int(math.sqrt(limit)) + 1):
        if sieve[i]:
            for j in range(i * i, limit + 1, i):
                sieve[j] = False

    # Collect primes
    return [i for i in range(2, limit + 1) if sieve[i]]

def get_primes_in_range(start: int, end: int) -> List[int]:
    """
    Get all prime numbers in a given range.

    Args:
        start: Start of the range (inclusive)
        end: End of the range (inclusive)

    Returns:
        List[int]: List of prime numbers in the range
    """
    primes = []
    for num in range(max(2, start), end + 1):
        if is_prime(num):
            primes.append(num)
    return primes

def prime_factorization(n: int) -> List[int]:
    """
    Find the prime factorization of a number.

    Args:
        n: The number to factorize

    Returns:
        List[int]: List of prime factors
    """
    factors = []
    d = 2
    while d * d <= n:
        while n % d == 0:
            factors.append(d)
            n //= d
        d += 1
    if n > 1:
        factors.append(n)
    return factors

def main():
    """Main interactive program."""
    print("🔢 Prime Number Generator and Checker")
    print("=" * 40)

    while True:
        print("\\nChoose an option:")
        print("1. Check if a number is prime")
        print("2. Generate primes up to a limit")
        print("3. Find primes in a range")
        print("4. Prime factorization")
        print("5. Performance test")
        print("6. Exit")

        try:
            choice = input("\\nEnter your choice (1-6): ").strip()

            if choice == '1':
                num = int(input("Enter a number to check: "))
                start_time = time.time()
                result = is_prime(num)
                end_time = time.time()
                print(f"{num} is {'prime' if result else 'not prime'}")
                print(f"Computation time: {(end_time - start_time) * 1000:.2f} ms")

            elif choice == '2':
                limit = int(input("Enter the upper limit: "))
                start_time = time.time()
                primes = sieve_of_eratosthenes(limit)
                end_time = time.time()
                print(f"Found {len(primes)} primes up to {limit}")
                if len(primes) <= 100:
                    print(f"Primes: {primes}")
                else:
                    print(f"First 10: {primes[:10]}")
                    print(f"Last 10: {primes[-10:]}")
                print(f"Computation time: {(end_time - start_time) * 1000:.2f} ms")

            elif choice == '3':
                start = int(input("Enter start of range: "))
                end = int(input("Enter end of range: "))
                start_time = time.time()
                primes = get_primes_in_range(start, end)
                end_time = time.time()
                print(f"Primes between {start} and {end}: {primes}")
                print(f"Count: {len(primes)}")
                print(f"Computation time: {(end_time - start_time) * 1000:.2f} ms")

            elif choice == '4':
                num = int(input("Enter a number to factorize: "))
                start_time = time.time()
                factors = prime_factorization(num)
                end_time = time.time()
                print(f"Prime factors of {num}: {factors}")
                print(f"Verification: {' × '.join(map(str, factors))} = {num}")
                print(f"Computation time: {(end_time - start_time) * 1000:.2f} ms")

            elif choice == '5':
                print("\\nPerformance Test - Finding primes up to 100,000")
                start_time = time.time()
                primes = sieve_of_eratosthenes(100000)
                end_time = time.time()
                print(f"Found {len(primes)} primes")
                print(f"Time taken: {(end_time - start_time) * 1000:.2f} ms")

            elif choice == '6':
                print("Thank you for using the Prime Number Generator!")
                break

            else:
                print("Invalid choice. Please enter 1-6.")

        except ValueError:
            print("Please enter a valid number.")
        except KeyboardInterrupt:
            print("\\nProgram interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()
`,
            'javascript': `/**
 * Prime Number Generator and Checker
 * Generated by Microchip AI Assistant
 *
 * This program provides comprehensive prime number functionality including:
 * - Prime number checking
 * - Sieve of Eratosthenes algorithm
 * - Prime number generation
 * - Interactive user interface
 */

const readline = require('readline');

/**
 * Check if a number is prime using optimized trial division
 * @param {number} n - The number to check
 * @returns {boolean} True if the number is prime, false otherwise
 */
function isPrime(n) {
    if (n < 2) return false;
    if (n === 2) return true;
    if (n % 2 === 0) return false;

    // Check odd divisors up to sqrt(n)
    for (let i = 3; i <= Math.sqrt(n); i += 2) {
        if (n % i === 0) return false;
    }
    return true;
}

/**
 * Generate all prime numbers up to a given limit using the Sieve of Eratosthenes
 * @param {number} limit - The upper limit for prime generation
 * @returns {number[]} Array of all prime numbers up to the limit
 */
function sieveOfEratosthenes(limit) {
    if (limit < 2) return [];

    // Initialize sieve
    const sieve = new Array(limit + 1).fill(true);
    sieve[0] = sieve[1] = false;

    // Apply sieve algorithm
    for (let i = 2; i <= Math.sqrt(limit); i++) {
        if (sieve[i]) {
            for (let j = i * i; j <= limit; j += i) {
                sieve[j] = false;
            }
        }
    }

    // Collect primes
    const primes = [];
    for (let i = 2; i <= limit; i++) {
        if (sieve[i]) primes.push(i);
    }
    return primes;
}

/**
 * Get all prime numbers in a given range
 * @param {number} start - Start of the range (inclusive)
 * @param {number} end - End of the range (inclusive)
 * @returns {number[]} Array of prime numbers in the range
 */
function getPrimesInRange(start, end) {
    const primes = [];
    for (let num = Math.max(2, start); num <= end; num++) {
        if (isPrime(num)) {
            primes.push(num);
        }
    }
    return primes;
}

/**
 * Find the prime factorization of a number
 * @param {number} n - The number to factorize
 * @returns {number[]} Array of prime factors
 */
function primeFactorization(n) {
    const factors = [];
    let d = 2;
    while (d * d <= n) {
        while (n % d === 0) {
            factors.push(d);
            n = Math.floor(n / d);
        }
        d++;
    }
    if (n > 1) {
        factors.push(n);
    }
    return factors;
}

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Main interactive program
 */
async function main() {
    console.log('🔢 Prime Number Generator and Checker');
    console.log('='.repeat(40));

    while (true) {
        console.log('\\nChoose an option:');
        console.log('1. Check if a number is prime');
        console.log('2. Generate primes up to a limit');
        console.log('3. Find primes in a range');
        console.log('4. Prime factorization');
        console.log('5. Performance test');
        console.log('6. Exit');

        try {
            const choice = await askQuestion('\\nEnter your choice (1-6): ');

            if (choice === '1') {
                const num = parseInt(await askQuestion('Enter a number to check: '));
                const startTime = performance.now();
                const result = isPrime(num);
                const endTime = performance.now();
                console.log(\`\${num} is \${result ? 'prime' : 'not prime'}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '2') {
                const limit = parseInt(await askQuestion('Enter the upper limit: '));
                const startTime = performance.now();
                const primes = sieveOfEratosthenes(limit);
                const endTime = performance.now();
                console.log(\`Found \${primes.length} primes up to \${limit}\`);
                if (primes.length <= 100) {
                    console.log(\`Primes: \${primes}\`);
                } else {
                    console.log(\`First 10: \${primes.slice(0, 10)}\`);
                    console.log(\`Last 10: \${primes.slice(-10)}\`);
                }
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '3') {
                const start = parseInt(await askQuestion('Enter start of range: '));
                const end = parseInt(await askQuestion('Enter end of range: '));
                const startTime = performance.now();
                const primes = getPrimesInRange(start, end);
                const endTime = performance.now();
                console.log(\`Primes between \${start} and \${end}: \${primes}\`);
                console.log(\`Count: \${primes.length}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '4') {
                const num = parseInt(await askQuestion('Enter a number to factorize: '));
                const startTime = performance.now();
                const factors = primeFactorization(num);
                const endTime = performance.now();
                console.log(\`Prime factors of \${num}: \${factors}\`);
                console.log(\`Verification: \${factors.join(' × ')} = \${num}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '5') {
                console.log('\\nPerformance Test - Finding primes up to 100,000');
                const startTime = performance.now();
                const primes = sieveOfEratosthenes(100000);
                const endTime = performance.now();
                console.log(\`Found \${primes.length} primes\`);
                console.log(\`Time taken: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '6') {
                console.log('Thank you for using the Prime Number Generator!');
                break;

            } else {
                console.log('Invalid choice. Please enter 1-6.');
            }

        } catch (error) {
            console.log('Please enter a valid number.');
        }
    }

    rl.close();
}

/**
 * Helper function to ask questions using readline
 * @param {string} question - The question to ask
 * @returns {Promise<string>} The user's answer
 */
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Run the program
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    isPrime,
    sieveOfEratosthenes,
    getPrimesInRange,
    primeFactorization
};
`,
            'typescript': `/**
 * Prime Number Generator and Checker
 * Generated by Microchip AI Assistant
 *
 * This program provides comprehensive prime number functionality including:
 * - Prime number checking
 * - Sieve of Eratosthenes algorithm
 * - Prime number generation
 * - Interactive user interface
 */

import * as readline from 'readline';

/**
 * Check if a number is prime using optimized trial division
 */
function isPrime(n: number): boolean {
    if (n < 2) return false;
    if (n === 2) return true;
    if (n % 2 === 0) return false;

    // Check odd divisors up to sqrt(n)
    for (let i = 3; i <= Math.sqrt(n); i += 2) {
        if (n % i === 0) return false;
    }
    return true;
}

/**
 * Generate all prime numbers up to a given limit using the Sieve of Eratosthenes
 */
function sieveOfEratosthenes(limit: number): number[] {
    if (limit < 2) return [];

    // Initialize sieve
    const sieve: boolean[] = new Array(limit + 1).fill(true);
    sieve[0] = sieve[1] = false;

    // Apply sieve algorithm
    for (let i = 2; i <= Math.sqrt(limit); i++) {
        if (sieve[i]) {
            for (let j = i * i; j <= limit; j += i) {
                sieve[j] = false;
            }
        }
    }

    // Collect primes
    const primes: number[] = [];
    for (let i = 2; i <= limit; i++) {
        if (sieve[i]) primes.push(i);
    }
    return primes;
}

/**
 * Get all prime numbers in a given range
 */
function getPrimesInRange(start: number, end: number): number[] {
    const primes: number[] = [];
    for (let num = Math.max(2, start); num <= end; num++) {
        if (isPrime(num)) {
            primes.push(num);
        }
    }
    return primes;
}

/**
 * Find the prime factorization of a number
 */
function primeFactorization(n: number): number[] {
    const factors: number[] = [];
    let d = 2;
    while (d * d <= n) {
        while (n % d === 0) {
            factors.push(d);
            n = Math.floor(n / d);
        }
        d++;
    }
    if (n > 1) {
        factors.push(n);
    }
    return factors;
}

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Helper function to ask questions using readline
 */
function askQuestion(question: string): Promise<string> {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

/**
 * Main interactive program
 */
async function main(): Promise<void> {
    console.log('🔢 Prime Number Generator and Checker');
    console.log('='.repeat(40));

    while (true) {
        console.log('\\nChoose an option:');
        console.log('1. Check if a number is prime');
        console.log('2. Generate primes up to a limit');
        console.log('3. Find primes in a range');
        console.log('4. Prime factorization');
        console.log('5. Performance test');
        console.log('6. Exit');

        try {
            const choice = await askQuestion('\\nEnter your choice (1-6): ');

            if (choice === '1') {
                const num = parseInt(await askQuestion('Enter a number to check: '));
                const startTime = performance.now();
                const result = isPrime(num);
                const endTime = performance.now();
                console.log(\`\${num} is \${result ? 'prime' : 'not prime'}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '2') {
                const limit = parseInt(await askQuestion('Enter the upper limit: '));
                const startTime = performance.now();
                const primes = sieveOfEratosthenes(limit);
                const endTime = performance.now();
                console.log(\`Found \${primes.length} primes up to \${limit}\`);
                if (primes.length <= 100) {
                    console.log(\`Primes: \${primes}\`);
                } else {
                    console.log(\`First 10: \${primes.slice(0, 10)}\`);
                    console.log(\`Last 10: \${primes.slice(-10)}\`);
                }
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '3') {
                const start = parseInt(await askQuestion('Enter start of range: '));
                const end = parseInt(await askQuestion('Enter end of range: '));
                const startTime = performance.now();
                const primes = getPrimesInRange(start, end);
                const endTime = performance.now();
                console.log(\`Primes between \${start} and \${end}: \${primes}\`);
                console.log(\`Count: \${primes.length}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '4') {
                const num = parseInt(await askQuestion('Enter a number to factorize: '));
                const startTime = performance.now();
                const factors = primeFactorization(num);
                const endTime = performance.now();
                console.log(\`Prime factors of \${num}: \${factors}\`);
                console.log(\`Verification: \${factors.join(' × ')} = \${num}\`);
                console.log(\`Computation time: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '5') {
                console.log('\\nPerformance Test - Finding primes up to 100,000');
                const startTime = performance.now();
                const primes = sieveOfEratosthenes(100000);
                const endTime = performance.now();
                console.log(\`Found \${primes.length} primes\`);
                console.log(\`Time taken: \${(endTime - startTime).toFixed(2)} ms\`);

            } else if (choice === '6') {
                console.log('Thank you for using the Prime Number Generator!');
                break;

            } else {
                console.log('Invalid choice. Please enter 1-6.');
            }

        } catch (error) {
            console.log('Please enter a valid number.');
        }
    }

    rl.close();
}

// Run the program
if (require.main === module) {
    main().catch(console.error);
}

export {
    isPrime,
    sieveOfEratosthenes,
    getPrimesInRange,
    primeFactorization
};
`
        };
        return templates[language] || templates['python'];
    }
    // Intelligent parsing and formatting methods
    async parseIntelligentWriteOperation(query) {
        // Extract file path and content from query
        const { filePath, content } = this.extractFilePathAndContentFromQuery(query);
        // Determine operation type
        const operation = await fs.pathExists(path.resolve(this.workspaceRoot, filePath)) ? 'modify' : 'create';
        // Analyze dependencies
        const dependencies = await this.analyzeDependencies(filePath, content);
        // Assess impact
        const impact = await this.assessImpact(filePath, operation);
        // Generate reasoning
        const reasoning = this.generateOperationReasoning(filePath, operation, dependencies);
        return {
            operation,
            targetPath: filePath,
            content,
            reasoning,
            dependencies,
            impact
        };
    }
    async validateFileOperation(operation) {
        // Check if file path is valid
        if (!operation.targetPath || operation.targetPath.includes('..')) {
            return { isValid: false, reason: 'Invalid file path' };
        }
        // Check for potential conflicts
        if (operation.operation === 'create') {
            const exists = await fs.pathExists(path.resolve(this.workspaceRoot, operation.targetPath));
            if (exists) {
                return { isValid: false, reason: 'File already exists. Use modify operation instead.' };
            }
        }
        return { isValid: true };
    }
    async applyIntelligentFormatting(content, filePath) {
        const ext = path.extname(filePath);
        // Apply language-specific formatting
        if (ext === '.ts' || ext === '.tsx' || ext === '.js' || ext === '.jsx') {
            return this.formatTypeScriptJavaScript(content);
        }
        else if (ext === '.json') {
            try {
                return JSON.stringify(JSON.parse(content), null, this.codeContext?.conventions.indentSize || 2);
            }
            catch {
                return content;
            }
        }
        return content;
    }
    formatTypeScriptJavaScript(content) {
        // Basic formatting
        const lines = content.split('\n');
        const indentSize = this.codeContext?.conventions.indentSize || 2;
        const useSpaces = this.codeContext?.conventions.indentation === 'spaces';
        const indent = useSpaces ? ' '.repeat(indentSize) : '\t';
        let indentLevel = 0;
        const formattedLines = lines.map(line => {
            const trimmed = line.trim();
            if (trimmed.endsWith('{')) {
                const formatted = indent.repeat(indentLevel) + trimmed;
                indentLevel++;
                return formatted;
            }
            else if (trimmed.startsWith('}')) {
                indentLevel = Math.max(0, indentLevel - 1);
                return indent.repeat(indentLevel) + trimmed;
            }
            else if (trimmed) {
                return indent.repeat(indentLevel) + trimmed;
            }
            return '';
        });
        return formattedLines.join('\n');
    }
    async analyzeDependencies(filePath, content) {
        const dependencies = [];
        const ext = path.extname(filePath);
        if (ext === '.ts' || ext === '.tsx' || ext === '.js' || ext === '.jsx') {
            // Extract import statements
            const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
            let match;
            while ((match = importRegex.exec(content)) !== null) {
                const importPath = match[1];
                if (importPath.startsWith('./') || importPath.startsWith('../')) {
                    dependencies.push(importPath);
                }
            }
        }
        return dependencies;
    }
    async assessImpact(filePath, operation) {
        // Assess based on file type and location
        const isConfigFile = ['package.json', 'tsconfig.json', 'eslint.config.js'].includes(path.basename(filePath));
        const isMainFile = filePath.includes('main.') || filePath.includes('index.') || filePath.includes('app.');
        if (isConfigFile)
            return 'high';
        if (isMainFile)
            return 'medium';
        if (operation === 'create')
            return 'low';
        return 'medium';
    }
    generateOperationReasoning(filePath, operation, dependencies) {
        const ext = path.extname(filePath);
        const fileName = path.basename(filePath);
        let reasoning = `${operation === 'create' ? 'Creating' : 'Modifying'} ${fileName}`;
        if (ext === '.ts' || ext === '.tsx') {
            reasoning += ' (TypeScript file)';
        }
        else if (ext === '.js' || ext === '.jsx') {
            reasoning += ' (JavaScript file)';
        }
        if (dependencies.length > 0) {
            reasoning += ` with ${dependencies.length} local dependencies`;
        }
        return reasoning;
    }
    async updateDependencies(operation) {
        // Update import statements in dependent files if needed
        for (const dep of operation.dependencies) {
            console.log(`Would update dependency: ${dep}`);
        }
    }
    // Project pattern detection methods
    async detectProjectPatterns() {
        const patterns = [];
        // Check for common patterns
        const hasComponents = await fs.pathExists(path.join(this.workspaceRoot, 'src/components'));
        const hasServices = await fs.pathExists(path.join(this.workspaceRoot, 'src/services'));
        const hasWebview = await fs.pathExists(path.join(this.workspaceRoot, 'src/webview'));
        if (hasComponents) {
            patterns.push({
                name: 'React Components',
                pattern: /src\/components\/.*\.(tsx?|jsx?)$/,
                description: 'React component files in src/components directory',
                examples: ['src/components/Button.tsx', 'src/components/Modal.jsx']
            });
        }
        if (hasServices) {
            patterns.push({
                name: 'Service Layer',
                pattern: /src\/services\/.*\.ts$/,
                description: 'Service layer files for business logic',
                examples: ['src/services/ApiService.ts', 'src/services/DataService.ts']
            });
        }
        if (hasWebview) {
            patterns.push({
                name: 'VS Code Webview',
                pattern: /src\/webview\/.*\.(tsx?|jsx?)$/,
                description: 'VS Code webview components',
                examples: ['src/webview/App.tsx', 'src/webview/Provider.ts']
            });
        }
        return patterns;
    }
    async getSampleFiles() {
        try {
            const files = await (0, glob_1.glob)('src/**/*.{ts,tsx,js,jsx}', {
                cwd: this.workspaceRoot,
                ignore: ['node_modules/**'],
                nodir: true
            });
            return files.slice(0, 5); // Get first 5 files for analysis
        }
        catch (error) {
            return [];
        }
    }
    async analyzeCodeConventions(sampleFiles) {
        let spaceCount = 0;
        let tabCount = 0;
        let singleQuoteCount = 0;
        let doubleQuoteCount = 0;
        let indentSizes = [];
        for (const file of sampleFiles) {
            try {
                const content = await fs.readFile(path.join(this.workspaceRoot, file), 'utf-8');
                const lines = content.split('\n');
                for (const line of lines) {
                    // Check indentation
                    const leadingSpaces = line.match(/^( +)/);
                    const leadingTabs = line.match(/^(\t+)/);
                    if (leadingSpaces) {
                        spaceCount++;
                        indentSizes.push(leadingSpaces[1].length);
                    }
                    if (leadingTabs) {
                        tabCount++;
                    }
                    // Check quotes
                    const singleQuotes = (line.match(/'/g) || []).length;
                    const doubleQuotes = (line.match(/"/g) || []).length;
                    singleQuoteCount += singleQuotes;
                    doubleQuoteCount += doubleQuotes;
                }
            }
            catch (error) {
                // Skip files that can't be read
            }
        }
        // Determine conventions
        const indentation = spaceCount > tabCount ? 'spaces' : 'tabs';
        const quotes = singleQuoteCount > doubleQuoteCount ? 'single' : 'double';
        const indentSize = indentSizes.length > 0 ?
            Math.round(indentSizes.reduce((a, b) => a + b, 0) / indentSizes.length) : 2;
        return {
            naming: 'camelCase', // Default, could be enhanced with more analysis
            indentation,
            indentSize: Math.max(2, Math.min(8, indentSize)), // Clamp between 2-8
            quotes
        };
    }
    getAllTools() {
        return [
            // Enhanced intelligent tools
            this.createIntelligentReadFileTool(),
            this.createIntelligentWriteFileTool(),
            // Original tools
            this.createReadFileTool(),
            this.createWriteFileTool(),
            this.createCreateFileTool(),
            this.createMoveFileTool(),
            this.createListFilesTool(),
            this.createSearchCodeTool(),
            this.createGetActiveFileContextTool(),
            this.createExtractFunctionTool(),
            this.createMoveCodeBetweenFilesTool(),
            this.createGenerateCodeTool(),
            this.createRefactorCodeTool(),
            this.createPrimeNumberProgramTool(),
            // Command execution and terminal tools
            this.createExecuteCommandTool(),
            this.createGitOperationsTool(),
            this.createNpmOperationsTool(),
            this.createTestRunnerTool()
        ];
    }
    // Command execution tool
    createExecuteCommandTool() {
        return {
            name: 'execute_command',
            description: 'Execute terminal commands (npm install, git operations, build scripts, etc.)',
            keywords: ['command', 'terminal', 'execute', 'run', 'shell', 'npm', 'git', 'build'],
            execute: async (query, context) => {
                try {
                    const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
                    const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
                    const execAsync = promisify(exec);
                    // Parse command from query
                    const commandMatch = query.match(/(?:run|execute|command)\s+(.+)/i);
                    if (!commandMatch) {
                        return 'Please specify a command to execute. Example: "execute npm install"';
                    }
                    const command = commandMatch[1].trim();
                    // Security check - only allow safe commands
                    const allowedCommands = ['npm', 'yarn', 'git', 'node', 'tsc', 'eslint', 'prettier', 'test'];
                    const commandStart = command.split(' ')[0];
                    if (!allowedCommands.includes(commandStart)) {
                        return `Command "${commandStart}" is not allowed for security reasons. Allowed commands: ${allowedCommands.join(', ')}`;
                    }
                    const { stdout, stderr } = await execAsync(command, {
                        cwd: this.workspaceRoot,
                        timeout: 30000 // 30 second timeout
                    });
                    let result = `Command executed: ${command}\n\n`;
                    if (stdout)
                        result += `Output:\n${stdout}\n`;
                    if (stderr)
                        result += `Warnings/Errors:\n${stderr}\n`;
                    return result;
                }
                catch (error) {
                    return `Command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Git operations tool
    createGitOperationsTool() {
        return {
            name: 'git_operations',
            description: 'Perform git operations - status, add, commit, push, pull, branch operations',
            keywords: ['git', 'commit', 'push', 'pull', 'status', 'branch', 'version control'],
            execute: async (query, context) => {
                try {
                    const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
                    const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
                    const execAsync = promisify(exec);
                    let gitCommand = '';
                    if (query.includes('status')) {
                        gitCommand = 'git status';
                    }
                    else if (query.includes('add')) {
                        const fileMatch = query.match(/add\s+(.+)/i);
                        gitCommand = fileMatch ? `git add ${fileMatch[1]}` : 'git add .';
                    }
                    else if (query.includes('commit')) {
                        const messageMatch = query.match(/commit\s+(?:with\s+message\s+)?["\']([^"\']+)["\']|commit\s+(.+)/i);
                        const message = messageMatch ? (messageMatch[1] || messageMatch[2]) : 'Auto-commit from AI assistant';
                        gitCommand = `git commit -m "${message}"`;
                    }
                    else if (query.includes('push')) {
                        gitCommand = 'git push';
                    }
                    else if (query.includes('pull')) {
                        gitCommand = 'git pull';
                    }
                    else if (query.includes('branch')) {
                        const branchMatch = query.match(/(?:create\s+)?branch\s+(\w+)/i);
                        gitCommand = branchMatch ? `git checkout -b ${branchMatch[1]}` : 'git branch';
                    }
                    else {
                        return 'Please specify a git operation: status, add, commit, push, pull, or branch';
                    }
                    const { stdout, stderr } = await execAsync(gitCommand, {
                        cwd: this.workspaceRoot,
                        timeout: 15000
                    });
                    let result = `Git command executed: ${gitCommand}\n\n`;
                    if (stdout)
                        result += `Output:\n${stdout}\n`;
                    if (stderr)
                        result += `Messages:\n${stderr}\n`;
                    return result;
                }
                catch (error) {
                    return `Git operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // NPM operations tool
    createNpmOperationsTool() {
        return {
            name: 'npm_operations',
            description: 'Perform npm operations - install, uninstall, update, run scripts',
            keywords: ['npm', 'install', 'uninstall', 'update', 'script', 'package', 'dependency'],
            execute: async (query, context) => {
                try {
                    const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
                    const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
                    const execAsync = promisify(exec);
                    let npmCommand = '';
                    if (query.includes('install')) {
                        const packageMatch = query.match(/install\s+(.+)/i);
                        if (packageMatch) {
                            const packages = packageMatch[1].trim();
                            const isDev = query.includes('dev') || query.includes('development');
                            npmCommand = `npm install ${packages}${isDev ? ' --save-dev' : ''}`;
                        }
                        else {
                            npmCommand = 'npm install';
                        }
                    }
                    else if (query.includes('uninstall') || query.includes('remove')) {
                        const packageMatch = query.match(/(?:uninstall|remove)\s+(.+)/i);
                        if (packageMatch) {
                            npmCommand = `npm uninstall ${packageMatch[1].trim()}`;
                        }
                        else {
                            return 'Please specify package(s) to uninstall';
                        }
                    }
                    else if (query.includes('update')) {
                        const packageMatch = query.match(/update\s+(.+)/i);
                        npmCommand = packageMatch ? `npm update ${packageMatch[1].trim()}` : 'npm update';
                    }
                    else if (query.includes('run') || query.includes('script')) {
                        const scriptMatch = query.match(/(?:run|script)\s+(\w+)/i);
                        if (scriptMatch) {
                            npmCommand = `npm run ${scriptMatch[1]}`;
                        }
                        else {
                            return 'Please specify script name to run';
                        }
                    }
                    else {
                        return 'Please specify an npm operation: install, uninstall, update, or run script';
                    }
                    const { stdout, stderr } = await execAsync(npmCommand, {
                        cwd: this.workspaceRoot,
                        timeout: 60000 // 60 seconds for npm operations
                    });
                    let result = `NPM command executed: ${npmCommand}\n\n`;
                    if (stdout)
                        result += `Output:\n${stdout}\n`;
                    if (stderr)
                        result += `Messages:\n${stderr}\n`;
                    return result;
                }
                catch (error) {
                    return `NPM operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
    // Test runner tool
    createTestRunnerTool() {
        return {
            name: 'test_runner',
            description: 'Run tests and analyze test results',
            keywords: ['test', 'testing', 'jest', 'mocha', 'spec', 'unit test', 'integration test'],
            execute: async (query, context) => {
                try {
                    const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
                    const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
                    const execAsync = promisify(exec);
                    let testCommand = '';
                    if (query.includes('all') || query.includes('run tests')) {
                        testCommand = 'npm test';
                    }
                    else if (query.includes('watch')) {
                        testCommand = 'npm run test:watch';
                    }
                    else if (query.includes('coverage')) {
                        testCommand = 'npm run test:coverage';
                    }
                    else {
                        const fileMatch = query.match(/test\s+(.+\.(?:test|spec)\.\w+)/i);
                        if (fileMatch) {
                            testCommand = `npm test -- ${fileMatch[1]}`;
                        }
                        else {
                            testCommand = 'npm test';
                        }
                    }
                    const { stdout, stderr } = await execAsync(testCommand, {
                        cwd: this.workspaceRoot,
                        timeout: 120000 // 2 minutes for tests
                    });
                    let result = `Test command executed: ${testCommand}\n\n`;
                    if (stdout)
                        result += `Test Results:\n${stdout}\n`;
                    if (stderr)
                        result += `Test Messages:\n${stderr}\n`;
                    // Parse test results for summary
                    const passMatch = stdout.match(/(\d+)\s+passing/i);
                    const failMatch = stdout.match(/(\d+)\s+failing/i);
                    if (passMatch || failMatch) {
                        result += '\nTest Summary:\n';
                        if (passMatch)
                            result += `✅ ${passMatch[1]} tests passing\n`;
                        if (failMatch)
                            result += `❌ ${failMatch[1]} tests failing\n`;
                    }
                    return result;
                }
                catch (error) {
                    return `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        };
    }
}
exports.AdvancedAgentTools = AdvancedAgentTools;
//# sourceMappingURL=AdvancedAgentTools.js.map