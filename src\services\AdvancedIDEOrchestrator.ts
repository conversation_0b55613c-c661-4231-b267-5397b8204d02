import * as vscode from 'vscode';
import { AdvancedAgentTools } from './AdvancedAgentTools';
import { AdvancedCodeAnalysis } from './AdvancedCodeAnalysis';
import { IntelligentSearch } from './IntelligentSearch';
import { IntelligentCodeGeneration } from './IntelligentCodeGeneration';
import { AutomatedRefactoring } from './AutomatedRefactoring';
import { ErrorDetectionResolution } from './ErrorDetectionResolution';
import { IntegrationAutomation } from './IntegrationAutomation';
import { UserInteractionIntelligence } from './UserInteractionIntelligence';
import { LangChainAgentService } from './LangChainAgentService';
import { AutomatedTestingService } from './AutomatedTestingService';
import { GitIntegrationService } from './GitIntegrationService';
import { TerminalIntegrationService } from './TerminalIntegrationService';

// Master Orchestrator for Advanced IDE Capabilities
export interface IDECapability {
  name: string;
  description: string;
  category: 'analysis' | 'generation' | 'refactoring' | 'search' | 'automation' | 'interaction';
  enabled: boolean;
  priority: number;
}

export interface IDECommand {
  id: string;
  name: string;
  description: string;
  category: string;
  handler: (args?: any) => Promise<any>;
  shortcut?: string;
}

export interface IDEWorkspace {
  root: string;
  type: 'extension' | 'application' | 'library';
  language: 'typescript' | 'javascript' | 'mixed';
  framework?: string;
  capabilities: IDECapability[];
}

export class AdvancedIDEOrchestrator {
  private workspace: IDEWorkspace;
  private services: Map<string, any> = new Map();
  private commands: Map<string, IDECommand> = new Map();
  private isInitialized: boolean = false;

  // Core Services
  private agentTools!: AdvancedAgentTools;
  private codeAnalysis!: AdvancedCodeAnalysis;
  private intelligentSearch!: IntelligentSearch;
  private codeGeneration!: IntelligentCodeGeneration;
  private refactoring!: AutomatedRefactoring;
  private errorDetection!: ErrorDetectionResolution;
  private automation!: IntegrationAutomation;
  private userInteraction!: UserInteractionIntelligence;
  private langChainAgent!: LangChainAgentService;
  private testingService!: AutomatedTestingService;
  private gitService!: GitIntegrationService;
  private terminalService!: TerminalIntegrationService;

  constructor(workspaceRoot?: string) {
    const root = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    
    this.workspace = {
      root,
      type: 'extension',
      language: 'typescript',
      capabilities: []
    };

    this.initializeServices();
    this.registerCommands();
  }

  // Initialize all advanced IDE services
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize core services
      await this.langChainAgent.initialize();
      
      // Analyze workspace
      await this.analyzeWorkspace();
      
      // Setup integrations
      await this.setupIntegrations();
      
      this.isInitialized = true;
      
      vscode.window.showInformationMessage('🚀 Advanced IDE capabilities initialized successfully!');
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to initialize IDE capabilities: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  // Execute intelligent command
  async executeCommand(commandId: string, args?: any): Promise<any> {
    const command = this.commands.get(commandId);
    if (!command) {
      throw new Error(`Command ${commandId} not found`);
    }

    try {
      const result = await command.handler(args);
      
      // Log command execution for learning
      await this.userInteraction.trackFeedback(commandId, 'helpful');
      
      return result;
    } catch (error) {
      vscode.window.showErrorMessage(`Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  // Intelligent code assistance
  async provideCodeAssistance(query: string): Promise<string> {
    // Analyze user intent
    const intent = await this.userInteraction.analyzeUserInput(query);
    
    // Route to appropriate service based on intent
    let response: string;
    
    switch (intent.category) {
      case 'code':
        response = await this.handleCodeRequest(query, intent);
        break;
      case 'debug':
        response = await this.handleDebugRequest(query, intent);
        break;
      case 'refactor':
        response = await this.handleRefactorRequest(query, intent);
        break;
      case 'generate':
        response = await this.handleGenerateRequest(query, intent);
        break;
      case 'search':
        response = await this.handleSearchRequest(query, intent);
        break;
      default:
        response = (await this.langChainAgent.executeQuery(query)).output;
    }

    return this.userInteraction.generateResponse(intent, response);
  }

  // Get proactive suggestions
  async getProactiveSuggestions(): Promise<any[]> {
    const suggestions = await this.userInteraction.getProactiveSuggestions();
    
    // Add technical suggestions
    const diagnostics = await this.errorDetection.detectErrors();
    if (diagnostics.errors.length > 0) {
      suggestions.push({
        id: 'fix-errors',
        type: 'warning',
        title: `${diagnostics.errors.length} issues detected`,
        description: 'Run error detection and get automated fixes',
        action: 'ide.detectErrors',
        priority: 'high',
        relevanceScore: 0.9,
        dismissible: false
      });
    }

    return suggestions;
  }

  // Initialize services
  private initializeServices(): void {
    this.agentTools = new AdvancedAgentTools(this.workspace.root);
    this.codeAnalysis = new AdvancedCodeAnalysis(this.workspace.root);
    this.intelligentSearch = new IntelligentSearch(this.workspace.root);
    this.codeGeneration = new IntelligentCodeGeneration(this.workspace.root);
    this.refactoring = new AutomatedRefactoring(this.workspace.root);
    this.errorDetection = new ErrorDetectionResolution(this.workspace.root);
    this.automation = new IntegrationAutomation(this.workspace.root);
    this.userInteraction = new UserInteractionIntelligence();
    this.langChainAgent = new LangChainAgentService(
      process.env.OPENAI_API_KEY || '',
      this.workspace.root,
      {
        history: [],
        userPreferences: {},
        sessionData: {},
        workspaceRoot: this.workspace.root
      }
    );
    this.testingService = new AutomatedTestingService(this.workspace.root);
    this.gitService = new GitIntegrationService(this.workspace.root);
    this.terminalService = new TerminalIntegrationService(this.workspace.root);

    // Register services
    this.services.set('agentTools', this.agentTools);
    this.services.set('codeAnalysis', this.codeAnalysis);
    this.services.set('intelligentSearch', this.intelligentSearch);
    this.services.set('codeGeneration', this.codeGeneration);
    this.services.set('refactoring', this.refactoring);
    this.services.set('errorDetection', this.errorDetection);
    this.services.set('automation', this.automation);
    this.services.set('userInteraction', this.userInteraction);
    this.services.set('langChainAgent', this.langChainAgent);
    this.services.set('testingService', this.testingService);
    this.services.set('gitService', this.gitService);
    this.services.set('terminalService', this.terminalService);
  }

  // Register IDE commands
  private registerCommands(): void {
    const commands: IDECommand[] = [
      {
        id: 'ide.analyzeProject',
        name: 'Analyze Project',
        description: 'Comprehensive project analysis with metrics and recommendations',
        category: 'analysis',
        handler: () => this.codeAnalysis.analyzeProject()
      },
      {
        id: 'ide.intelligentSearch',
        name: 'Intelligent Search',
        description: 'Multi-modal search with semantic understanding',
        category: 'search',
        handler: (query: string) => this.intelligentSearch.search(query)
      },
      {
        id: 'ide.generateCode',
        name: 'Generate Code',
        description: 'Context-aware code generation',
        category: 'generation',
        handler: (request: any) => this.codeGeneration.generateCode(request)
      },
      {
        id: 'ide.refactorCode',
        name: 'Refactor Code',
        description: 'Automated refactoring with intelligent suggestions',
        category: 'refactoring',
        handler: (filePath?: string) => this.refactoring.analyzeForRefactoring(filePath)
      },
      {
        id: 'ide.detectErrors',
        name: 'Detect Errors',
        description: 'Proactive error detection with automated fixes',
        category: 'analysis',
        handler: (filePath?: string) => this.errorDetection.detectErrors(filePath)
      },
      {
        id: 'ide.runAutomation',
        name: 'Run Automation',
        description: 'Execute automation workflows',
        category: 'automation',
        handler: (taskId: string) => this.automation.executeTask(taskId)
      },
      {
        id: 'ide.executeCommand',
        name: 'Execute Command',
        description: 'Execute terminal commands safely',
        category: 'automation',
        handler: (command: string) => this.executeTerminalCommand(command)
      },
      {
        id: 'ide.optimizeProject',
        name: 'Optimize Project',
        description: 'Apply project optimizations and best practices',
        category: 'automation',
        handler: () => this.automation.optimizeProject()
      },
      {
        id: 'ide.setupCI',
        name: 'Setup CI/CD',
        description: 'Setup continuous integration pipeline',
        category: 'automation',
        handler: () => this.automation.setupContinuousIntegration()
      },
      {
        id: 'ide.securityScan',
        name: 'Security Scan',
        description: 'Scan for security vulnerabilities',
        category: 'analysis',
        handler: (filePath?: string) => this.errorDetection.detectSecurityVulnerabilities(filePath)
      },
      {
        id: 'ide.performanceAnalysis',
        name: 'Performance Analysis',
        description: 'Analyze code for performance issues',
        category: 'analysis',
        handler: (filePath?: string) => this.errorDetection.detectPerformanceIssues(filePath)
      },
      {
        id: 'ide.generateReport',
        name: 'Generate Diagnostic Report',
        description: 'Generate comprehensive diagnostic report',
        category: 'analysis',
        handler: () => this.errorDetection.generateErrorReport()
      },
      {
        id: 'ide.runTests',
        name: 'Run Tests',
        description: 'Execute project tests',
        category: 'automation',
        handler: (options?: any) => this.testingService.runTests(options)
      },
      {
        id: 'ide.generateTests',
        name: 'Generate Tests',
        description: 'Generate test files for code',
        category: 'automation',
        handler: (request: any) => this.testingService.generateTests(request)
      },
      {
        id: 'ide.gitStatus',
        name: 'Git Status',
        description: 'Get git repository status',
        category: 'automation',
        handler: () => this.gitService.getStatus()
      },
      {
        id: 'ide.gitCommit',
        name: 'Git Commit',
        description: 'Commit changes to git',
        category: 'automation',
        handler: (message: string, files?: string[]) => this.gitService.commit(message, files)
      },
      {
        id: 'ide.runScript',
        name: 'Run Script',
        description: 'Execute npm/yarn script',
        category: 'automation',
        handler: (scriptName: string) => this.terminalService.runScript(scriptName)
      },
      {
        id: 'ide.installDependency',
        name: 'Install Dependency',
        description: 'Install npm package',
        category: 'automation',
        handler: (packageName: string, options?: any) => this.terminalService.addDependency(packageName, options)
      }
    ];

    commands.forEach(command => this.commands.set(command.id, command));
  }

  // Request handlers
  private async handleCodeRequest(query: string, intent: any): Promise<string> {
    // Use intelligent search to find relevant code
    const searchResults = await this.intelligentSearch.search(query);
    
    if (searchResults.length > 0) {
      const topResult = searchResults[0];
      return `Found relevant code in ${topResult.file} at line ${topResult.line}:\n\n${topResult.content}`;
    }
    
    return "I couldn't find specific code matching your query. Could you provide more details?";
  }

  private async handleDebugRequest(query: string, intent: any): Promise<string> {
    const diagnostics = await this.errorDetection.detectErrors();
    
    if (diagnostics.errors.length > 0) {
      const criticalErrors = diagnostics.errors.filter(e => e.severity === 'error');
      return `Found ${criticalErrors.length} critical errors. Here are the top issues:\n\n${criticalErrors.slice(0, 3).map(e => `- ${e.message} (${e.file}:${e.line})`).join('\n')}`;
    }
    
    return "No critical errors detected. Your code looks good!";
  }

  private async handleRefactorRequest(query: string, intent: any): Promise<string> {
    const refactoringPlan = await this.refactoring.analyzeForRefactoring();
    
    if (refactoringPlan.operations.length > 0) {
      const topOps = refactoringPlan.operations.slice(0, 3);
      return `Found ${refactoringPlan.operations.length} refactoring opportunities:\n\n${topOps.map(op => `- ${op.description} (${op.impact} impact)`).join('\n')}`;
    }
    
    return "Your code is well-structured! No major refactoring opportunities found.";
  }

  private async handleGenerateRequest(query: string, intent: any): Promise<string> {
    // Extract generation requirements from query
    const request = {
      type: 'component' as const,
      name: 'GeneratedComponent',
      description: query,
      options: {},
      context: {
        projectType: this.workspace.type,
        framework: this.workspace.framework || 'react',
        language: this.workspace.language,
        conventions: {
          naming: 'camelCase',
          indentation: 'spaces',
          quotes: 'single'
        },
        existingPatterns: [],
        dependencies: []
      }
    };

    const generated = await this.codeGeneration.generateCode(request);
    
    return `Generated ${generated.files.length} files:\n\n${generated.files.map(f => `- ${f.path}: ${f.description}`).join('\n')}\n\nInstructions:\n${generated.instructions.join('\n')}`;
  }

  private async handleSearchRequest(query: string, intent: any): Promise<string> {
    const results = await this.intelligentSearch.search(query);
    
    if (results.length > 0) {
      return `Found ${results.length} results:\n\n${results.slice(0, 5).map(r => `- ${r.file}:${r.line} - ${r.content.substring(0, 100)}...`).join('\n')}`;
    }
    
    return "No results found for your search query.";
  }

  // Analyze workspace
  private async analyzeWorkspace(): Promise<void> {
    // Detect project type and framework
    const analysis = await this.codeAnalysis.analyzeProject();
    
    this.workspace.capabilities = [
      { name: 'Code Analysis', description: 'Comprehensive code quality analysis', category: 'analysis', enabled: true, priority: 1 },
      { name: 'Intelligent Search', description: 'Multi-modal search capabilities', category: 'search', enabled: true, priority: 2 },
      { name: 'Code Generation', description: 'Context-aware code generation', category: 'generation', enabled: true, priority: 3 },
      { name: 'Automated Refactoring', description: 'Intelligent refactoring suggestions', category: 'refactoring', enabled: true, priority: 4 },
      { name: 'Error Detection', description: 'Proactive error detection and resolution', category: 'analysis', enabled: true, priority: 5 },
      { name: 'Automation', description: 'Development workflow automation', category: 'automation', enabled: true, priority: 6 }
    ];
  }

  // Setup integrations
  private async setupIntegrations(): Promise<void> {
    // Setup VS Code command palette integration
    this.commands.forEach((command, id) => {
      vscode.commands.registerCommand(id, command.handler);
    });

    // Setup status bar
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "🧠 Advanced IDE";
    statusBarItem.tooltip = "Advanced IDE capabilities active";
    statusBarItem.command = 'ide.analyzeProject';
    statusBarItem.show();
  }

  // Get service by name
  getService<T>(serviceName: string): T | undefined {
    return this.services.get(serviceName) as T;
  }

  // Execute terminal command safely
  private async executeTerminalCommand(command: string): Promise<string> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Security check - only allow safe commands
      const allowedCommands = ['npm', 'yarn', 'git', 'node', 'tsc', 'eslint', 'prettier', 'test'];
      const commandStart = command.split(' ')[0];

      if (!allowedCommands.includes(commandStart)) {
        throw new Error(`Command "${commandStart}" is not allowed for security reasons. Allowed commands: ${allowedCommands.join(', ')}`);
      }

      const { stdout, stderr } = await execAsync(command, {
        cwd: this.workspace.root,
        timeout: 30000 // 30 second timeout
      });

      let result = `Command executed: ${command}\n\n`;
      if (stdout) result += `Output:\n${stdout}\n`;
      if (stderr) result += `Warnings/Errors:\n${stderr}\n`;

      return result;
    } catch (error) {
      throw new Error(`Command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get workspace info
  getWorkspace(): IDEWorkspace {
    return { ...this.workspace };
  }

  // Cleanup
  dispose(): void {
    this.automation.dispose();
    this.terminalService.dispose();
    this.services.clear();
    this.commands.clear();
  }
}
