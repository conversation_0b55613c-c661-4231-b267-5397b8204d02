import * as vscode from 'vscode';

// User Interaction Intelligence Interfaces
export interface UserIntent {
  type: 'question' | 'request' | 'command' | 'exploration';
  category: 'code' | 'debug' | 'refactor' | 'generate' | 'explain' | 'search' | 'help';
  confidence: number;
  entities: IntentEntity[];
  context: UserContext;
}

export interface IntentEntity {
  type: 'file' | 'function' | 'variable' | 'concept' | 'technology';
  value: string;
  confidence: number;
  position?: { start: number; end: number };
}

export interface UserContext {
  currentFile?: string;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  recentActions: UserAction[];
  workspaceState: WorkspaceState;
  userPreferences: UserPreferences;
}

export interface UserAction {
  type: 'file-open' | 'text-select' | 'command-execute' | 'search' | 'edit';
  timestamp: Date;
  details: Record<string, any>;
}

export interface WorkspaceState {
  openFiles: string[];
  activeFile?: string;
  gitBranch?: string;
  lastModified: Date;
  projectType: string;
}

export interface UserPreferences {
  codeStyle: 'verbose' | 'concise' | 'detailed';
  explanationLevel: 'beginner' | 'intermediate' | 'expert';
  preferredLanguages: string[];
  autoSuggestions: boolean;
  proactiveHelp: boolean;
}

export interface ProactiveSuggestion {
  id: string;
  type: 'tip' | 'warning' | 'optimization' | 'learning';
  title: string;
  description: string;
  action?: string;
  priority: 'low' | 'medium' | 'high';
  relevanceScore: number;
  dismissible: boolean;
}

export interface ConversationMemory {
  sessionId: string;
  messages: ConversationMessage[];
  context: Record<string, any>;
  userProfile: UserProfile;
  startTime: Date;
  lastActivity: Date;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  intent?: UserIntent;
  metadata?: Record<string, any>;
}

export interface UserProfile {
  skillLevel: 'beginner' | 'intermediate' | 'expert';
  explanationLevel: 'beginner' | 'intermediate' | 'expert';
  preferredTopics: string[];
  learningGoals: string[];
  commonPatterns: string[];
  helpfulFeatures: string[];
}

export class UserInteractionIntelligence {
  private context: UserContext;
  private conversationMemory: ConversationMemory;
  private userProfile: UserProfile;
  private intentClassifier: IntentClassifier;
  private suggestionEngine: ProactiveSuggestionEngine;
  private adaptationEngine: AdaptationEngine;

  constructor() {
    this.context = this.initializeContext();
    this.conversationMemory = this.initializeConversationMemory();
    this.userProfile = this.loadUserProfile();
    this.intentClassifier = new IntentClassifier();
    this.suggestionEngine = new ProactiveSuggestionEngine(this.userProfile);
    this.adaptationEngine = new AdaptationEngine();
    
    this.setupContextTracking();
  }

  // Analyze user input and determine intent
  async analyzeUserInput(input: string): Promise<UserIntent> {
    const intent = await this.intentClassifier.classify(input, this.context);
    
    // Update conversation memory
    this.conversationMemory.messages.push({
      id: Date.now().toString(),
      role: 'user',
      content: input,
      timestamp: new Date(),
      intent
    });

    // Learn from user patterns
    await this.adaptationEngine.learnFromInteraction(intent, this.context);

    return intent;
  }

  // Generate contextual response
  async generateResponse(intent: UserIntent, input: string): Promise<string> {
    let response = '';

    switch (intent.category) {
      case 'code':
        response = await this.handleCodeRequest(intent, input);
        break;
      case 'debug':
        response = await this.handleDebugRequest(intent, input);
        break;
      case 'refactor':
        response = await this.handleRefactorRequest(intent, input);
        break;
      case 'generate':
        response = await this.handleGenerateRequest(intent, input);
        break;
      case 'explain':
        response = await this.handleExplainRequest(intent, input);
        break;
      case 'search':
        response = await this.handleSearchRequest(intent, input);
        break;
      default:
        response = await this.handleGeneralRequest(intent, input);
    }

    // Adapt response to user preferences
    response = this.adaptationEngine.adaptResponse(response, this.userProfile);

    // Store assistant response
    this.conversationMemory.messages.push({
      id: Date.now().toString(),
      role: 'assistant',
      content: response,
      timestamp: new Date()
    });

    return response;
  }

  // Get proactive suggestions
  async getProactiveSuggestions(): Promise<ProactiveSuggestion[]> {
    return this.suggestionEngine.generateSuggestions(this.context, this.conversationMemory);
  }

  // Update user preferences
  updateUserPreferences(preferences: Partial<UserPreferences>): void {
    this.context.userPreferences = { ...this.context.userPreferences, ...preferences };
    this.saveUserProfile();
  }

  // Track user feedback
  async trackFeedback(messageId: string, feedback: 'helpful' | 'not-helpful' | 'incorrect'): Promise<void> {
    const message = this.conversationMemory.messages.find(m => m.id === messageId);
    if (message) {
      message.metadata = { ...message.metadata, feedback };
      await this.adaptationEngine.learnFromFeedback(message, feedback);
    }
  }

  // Initialize context tracking
  private setupContextTracking(): void {
    // Track file changes
    vscode.window.onDidChangeActiveTextEditor((editor) => {
      if (editor) {
        this.context.currentFile = editor.document.fileName;
        this.context.recentActions.push({
          type: 'file-open',
          timestamp: new Date(),
          details: { file: editor.document.fileName }
        });
      }
    });

    // Track text selection
    vscode.window.onDidChangeTextEditorSelection((event) => {
      const selection = event.textEditor.selection;
      if (!selection.isEmpty) {
        this.context.selectedText = event.textEditor.document.getText(selection);
        this.context.cursorPosition = {
          line: selection.start.line,
          character: selection.start.character
        };
        this.context.recentActions.push({
          type: 'text-select',
          timestamp: new Date(),
          details: { text: this.context.selectedText }
        });
      }
    });
  }

  // Request handlers
  private async handleCodeRequest(intent: UserIntent, input: string): Promise<string> {
    const codeEntity = intent.entities.find(e => e.type === 'function' || e.type === 'variable');
    
    if (codeEntity) {
      return `I can help you with ${codeEntity.value}. Let me analyze the code and provide assistance.`;
    }
    
    return "I'm ready to help with your code. Could you provide more specific details about what you need?";
  }

  private async handleDebugRequest(intent: UserIntent, input: string): Promise<string> {
    if (this.context.selectedText) {
      return `I see you've selected some code. Let me help you debug this section. Common issues to check: variable initialization, null checks, and logic flow.`;
    }
    
    return "I can help you debug your code. Please share the problematic code or describe the issue you're experiencing.";
  }

  private async handleRefactorRequest(intent: UserIntent, input: string): Promise<string> {
    return "I can suggest refactoring improvements. Let me analyze your code for opportunities to improve structure, performance, and maintainability.";
  }

  private async handleGenerateRequest(intent: UserIntent, input: string): Promise<string> {
    const techEntity = intent.entities.find(e => e.type === 'technology');
    
    if (techEntity) {
      return `I can generate ${techEntity.value} code for you. What specific functionality do you need?`;
    }
    
    return "I can generate code based on your requirements. Please describe what you'd like me to create.";
  }

  private async handleExplainRequest(intent: UserIntent, input: string): Promise<string> {
    const conceptEntity = intent.entities.find(e => e.type === 'concept');
    
    if (conceptEntity) {
      const level = this.userProfile.skillLevel;
      return `Let me explain ${conceptEntity.value} at a ${level} level...`;
    }
    
    return "I'd be happy to explain any concept or code. What would you like me to clarify?";
  }

  private async handleSearchRequest(intent: UserIntent, input: string): Promise<string> {
    return "I can help you search through your codebase. What are you looking for?";
  }

  private async handleGeneralRequest(intent: UserIntent, input: string): Promise<string> {
    return "I'm here to help with your development needs. How can I assist you today?";
  }

  // Initialization methods
  private initializeContext(): UserContext {
    return {
      recentActions: [],
      workspaceState: {
        openFiles: [],
        lastModified: new Date(),
        projectType: 'unknown'
      },
      userPreferences: {
        codeStyle: 'concise',
        explanationLevel: 'intermediate',
        preferredLanguages: ['typescript', 'javascript'],
        autoSuggestions: true,
        proactiveHelp: true
      }
    };
  }

  private initializeConversationMemory(): ConversationMemory {
    return {
      sessionId: Date.now().toString(),
      messages: [],
      context: {},
      userProfile: this.userProfile,
      startTime: new Date(),
      lastActivity: new Date()
    };
  }

  private loadUserProfile(): UserProfile {
    // In a real implementation, this would load from persistent storage
    return {
      skillLevel: 'intermediate',
      explanationLevel: 'intermediate',
      preferredTopics: ['typescript', 'react', 'nodejs'],
      learningGoals: ['improve code quality', 'learn best practices'],
      commonPatterns: [],
      helpfulFeatures: []
    };
  }

  private saveUserProfile(): void {
    // In a real implementation, this would save to persistent storage
  }
}

// Intent Classification Engine
class IntentClassifier {
  async classify(input: string, context: UserContext): Promise<UserIntent> {
    const lowercaseInput = input.toLowerCase();
    
    // Simple rule-based classification (would be ML-based in production)
    let category: UserIntent['category'] = 'help';
    let type: UserIntent['type'] = 'question';
    
    if (lowercaseInput.includes('debug') || lowercaseInput.includes('error') || lowercaseInput.includes('fix')) {
      category = 'debug';
      type = 'request';
    } else if (lowercaseInput.includes('refactor') || lowercaseInput.includes('improve') || lowercaseInput.includes('optimize')) {
      category = 'refactor';
      type = 'request';
    } else if (lowercaseInput.includes('generate') || lowercaseInput.includes('create') || lowercaseInput.includes('make')) {
      category = 'generate';
      type = 'command';
    } else if (lowercaseInput.includes('explain') || lowercaseInput.includes('what is') || lowercaseInput.includes('how does')) {
      category = 'explain';
      type = 'question';
    } else if (lowercaseInput.includes('search') || lowercaseInput.includes('find')) {
      category = 'search';
      type = 'command';
    }

    return {
      type,
      category,
      confidence: 0.8,
      entities: this.extractEntities(input),
      context
    };
  }

  private extractEntities(input: string): IntentEntity[] {
    const entities: IntentEntity[] = [];
    
    // Simple entity extraction (would be more sophisticated in production)
    const words = input.split(/\s+/);
    
    words.forEach((word, index) => {
      if (word.endsWith('.ts') || word.endsWith('.js') || word.endsWith('.tsx') || word.endsWith('.jsx')) {
        entities.push({
          type: 'file',
          value: word,
          confidence: 0.9
        });
      }
    });

    return entities;
  }
}

// Proactive Suggestion Engine
class ProactiveSuggestionEngine {
  constructor(private userProfile: UserProfile) {}

  generateSuggestions(context: UserContext, memory: ConversationMemory): ProactiveSuggestion[] {
    const suggestions: ProactiveSuggestion[] = [];

    // Suggest based on current context
    if (context.currentFile?.endsWith('.ts') && context.selectedText) {
      suggestions.push({
        id: 'type-check',
        type: 'tip',
        title: 'Type Check Selected Code',
        description: 'Run TypeScript type checking on the selected code',
        priority: 'medium',
        relevanceScore: 0.8,
        dismissible: true
      });
    }

    return suggestions;
  }
}

// Adaptation Engine
class AdaptationEngine {
  async learnFromInteraction(intent: UserIntent, context: UserContext): Promise<void> {
    // Learn from user interaction patterns
  }

  async learnFromFeedback(message: ConversationMessage, feedback: string): Promise<void> {
    // Learn from user feedback
  }

  adaptResponse(response: string, userProfile: UserProfile): string {
    // Adapt response based on user preferences
    if (userProfile.explanationLevel === 'beginner') {
      return response + '\n\n💡 Tip: This is a common pattern in software development.';
    }
    
    return response;
  }
}
