# Build outputs
out/test/**
src/**
.vscode-test/**
.vscode/**

# Development files
.gitignore
.env.example
tsconfig*.json
vite.config.ts
vite.webview.config.ts
eslint.config.js

# Source files (keep only compiled output)
src/components/**
src/main.tsx
src/App.tsx
src/App.css
src/index.css
src/assets/**
src/webview/**/*.tsx
src/webview/**/*.ts
!src/webview/ChatbotWebviewProvider.ts

# Node modules (keep only necessary runtime deps)
node_modules/**
!node_modules/cors/**
!node_modules/express/**
!node_modules/dotenv/**

# Development and build artifacts
dist/index.html
public/**
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Package files
*.vsix
