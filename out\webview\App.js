"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const ApiKeyInput_1 = require("./ApiKeyInput");
const Chatbot_1 = require("./Chatbot");
require("../App.css");
function App() {
    const [isApiKeySet, setIsApiKeySet] = (0, react_1.useState)(false);
    const [apiKey, setApiKey] = (0, react_1.useState)('');
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    (0, react_1.useEffect)(() => {
        // Listen for messages from the extension
        const handleMessage = (event) => {
            const message = event.data;
            switch (message.type) {
                case 'apiKeyResponse':
                    if (message.apiKey) {
                        setApiKey(message.apiKey);
                        setIsApiKeySet(true);
                    }
                    setIsLoading(false);
                    break;
                case 'apiKeyCleared':
                    setApiKey('');
                    setIsApiKeySet(false);
                    break;
            }
        };
        window.addEventListener('message', handleMessage);
        // Request API key from extension
        if (window.vscode) {
            window.vscode.postMessage({ type: 'getApiKey' });
        }
        else {
            setIsLoading(false);
        }
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []);
    const handleApiKeySet = (isValid, newApiKey) => {
        setIsApiKeySet(isValid);
        if (isValid && newApiKey) {
            setApiKey(newApiKey);
            // Save API key to extension storage
            if (window.vscode) {
                window.vscode.postMessage({
                    type: 'saveApiKey',
                    apiKey: newApiKey
                });
            }
        }
    };
    const handleDisconnect = () => {
        setIsApiKeySet(false);
        setApiKey('');
        // Clear API key from extension storage
        if (window.vscode) {
            window.vscode.postMessage({ type: 'clearApiKey' });
        }
    };
    if (isLoading) {
        return (<div className="app">
        <div className="loading-container">
          <div className="spinner"></div>
          <p>Loading...</p>
        </div>
      </div>);
    }
    return (<div className="app">
      {!isApiKeySet ? (<ApiKeyInput_1.ApiKeyInput onApiKeySet={handleApiKeySet} initialApiKey={apiKey}/>) : (<Chatbot_1.Chatbot onDisconnect={handleDisconnect} apiKey={apiKey}/>)}
    </div>);
}
exports.default = App;
//# sourceMappingURL=App.js.map