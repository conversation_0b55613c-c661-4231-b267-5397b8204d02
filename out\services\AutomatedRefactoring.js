"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomatedRefactoring = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const vscode = __importStar(require("vscode"));
class AutomatedRefactoring {
    constructor(workspaceRoot) {
        this.patterns = [];
        this.analysisCache = new Map();
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.initializePatterns();
    }
    // Analyze code and suggest refactoring opportunities
    async analyzeForRefactoring(filePath) {
        const files = filePath ? [filePath] : await this.getAllProjectFiles();
        const operations = [];
        for (const file of files) {
            const fileOperations = await this.analyzeFile(file);
            operations.push(...fileOperations);
        }
        // Sort by impact and confidence
        operations.sort((a, b) => {
            const aScore = this.calculateOperationScore(a);
            const bScore = this.calculateOperationScore(b);
            return bScore - aScore;
        });
        return {
            operations: operations.slice(0, 20), // Top 20 opportunities
            estimatedTime: this.estimateRefactoringTime(operations),
            riskLevel: this.assessRiskLevel(operations),
            prerequisites: this.identifyPrerequisites(operations),
            benefits: this.identifyBenefits(operations)
        };
    }
    // Apply refactoring operations
    async applyRefactoring(operations) {
        const result = {
            success: true,
            operationsApplied: 0,
            filesModified: [],
            errors: [],
            warnings: [],
            summary: ''
        };
        // Group operations by file
        const operationsByFile = this.groupOperationsByFile(operations);
        for (const [file, fileOps] of operationsByFile.entries()) {
            try {
                await this.applyFileOperations(file, fileOps);
                result.operationsApplied += fileOps.length;
                result.filesModified.push(file);
            }
            catch (error) {
                result.errors.push(`Failed to refactor ${file}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                result.success = false;
            }
        }
        result.summary = this.generateRefactoringSummary(result);
        return result;
    }
    // Extract function refactoring
    async extractFunction(filePath, startLine, endLine, functionName) {
        const content = await this.getFileContent(filePath);
        if (!content)
            throw new Error('File not found');
        const lines = content.split('\n');
        const extractedCode = lines.slice(startLine - 1, endLine).join('\n');
        // Analyze extracted code for parameters and return value
        const analysis = this.analyzeExtractedCode(extractedCode);
        const newFunction = this.generateExtractedFunction(functionName, extractedCode, analysis);
        const functionCall = this.generateFunctionCall(functionName, analysis.parameters);
        // Replace original code with function call
        const newCode = [
            ...lines.slice(0, startLine - 1),
            functionCall,
            ...lines.slice(endLine),
            '',
            newFunction
        ].join('\n');
        return {
            type: 'extract-function',
            description: `Extract function ${functionName}`,
            file: filePath,
            startLine,
            endLine,
            newCode,
            impact: 'medium',
            confidence: 0.8,
            dependencies: []
        };
    }
    // Rename symbol refactoring
    async renameSymbol(filePath, oldName, newName) {
        const operations = [];
        const files = await this.findSymbolReferences(oldName);
        for (const file of files) {
            const content = await this.getFileContent(file);
            if (!content)
                continue;
            const newContent = this.replaceSymbolName(content, oldName, newName);
            operations.push({
                type: 'rename',
                description: `Rename ${oldName} to ${newName}`,
                file,
                startLine: 1,
                endLine: content.split('\n').length,
                newCode: newContent,
                impact: 'medium',
                confidence: 0.9,
                dependencies: files.filter(f => f !== file)
            });
        }
        return operations;
    }
    // Optimize imports
    async optimizeImports(filePath) {
        const content = await this.getFileContent(filePath);
        if (!content)
            throw new Error('File not found');
        const optimizedContent = this.optimizeFileImports(content);
        return {
            type: 'optimize',
            description: 'Optimize imports',
            file: filePath,
            startLine: 1,
            endLine: content.split('\n').length,
            newCode: optimizedContent,
            impact: 'low',
            confidence: 0.95,
            dependencies: []
        };
    }
    // Initialize refactoring patterns
    initializePatterns() {
        this.patterns = [
            {
                name: 'Convert to arrow function',
                pattern: /function\s+(\w+)\s*\([^)]*\)\s*{/g,
                replacement: 'const $1 = () => {',
                description: 'Convert function declaration to arrow function',
                category: 'readability'
            },
            {
                name: 'Use const for immutable variables',
                pattern: /let\s+(\w+)\s*=\s*([^;]+);(?!\s*\1\s*=)/g,
                replacement: 'const $1 = $2;',
                description: 'Use const instead of let for variables that are not reassigned',
                category: 'maintainability'
            },
            {
                name: 'Remove console.log',
                pattern: /console\.log\([^)]*\);\s*\n?/g,
                replacement: '',
                description: 'Remove console.log statements',
                category: 'performance'
            },
            {
                name: 'Use template literals',
                pattern: /'([^']*)'?\s*\+\s*(\w+)\s*\+\s*'([^']*)'/g,
                replacement: '`$1${$2}$3`',
                description: 'Convert string concatenation to template literals',
                category: 'readability'
            }
        ];
    }
    // Analyze individual file for refactoring opportunities
    async analyzeFile(filePath) {
        const content = await this.getFileContent(filePath);
        if (!content)
            return [];
        const operations = [];
        // Apply pattern-based refactoring
        for (const pattern of this.patterns) {
            const matches = content.match(pattern.pattern);
            if (matches) {
                const newContent = content.replace(pattern.pattern, pattern.replacement);
                operations.push({
                    type: 'optimize',
                    description: pattern.description,
                    file: filePath,
                    startLine: 1,
                    endLine: content.split('\n').length,
                    newCode: newContent,
                    impact: 'low',
                    confidence: 0.8,
                    dependencies: []
                });
            }
        }
        // Detect long functions
        const longFunctions = this.detectLongFunctions(content, filePath);
        operations.push(...longFunctions);
        // Detect duplicate code
        const duplicateCode = this.detectDuplicateCode(content, filePath);
        operations.push(...duplicateCode);
        return operations;
    }
    // Detect long functions that should be extracted
    detectLongFunctions(content, filePath) {
        const operations = [];
        const lines = content.split('\n');
        let inFunction = false;
        let functionStart = 0;
        let braceCount = 0;
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('function') || line.includes('=>')) {
                inFunction = true;
                functionStart = i;
                braceCount = 0;
            }
            if (inFunction) {
                braceCount += (line.match(/{/g) || []).length;
                braceCount -= (line.match(/}/g) || []).length;
                if (braceCount === 0 && i - functionStart > 30) {
                    operations.push({
                        type: 'extract-function',
                        description: `Long function detected (${i - functionStart} lines)`,
                        file: filePath,
                        startLine: functionStart + 1,
                        endLine: i + 1,
                        newCode: '', // Would be generated during extraction
                        impact: 'medium',
                        confidence: 0.7,
                        dependencies: []
                    });
                    inFunction = false;
                }
            }
        }
        return operations;
    }
    // Detect duplicate code blocks
    detectDuplicateCode(content, filePath) {
        const operations = [];
        const lines = content.split('\n');
        const minBlockSize = 5;
        for (let i = 0; i < lines.length - minBlockSize; i++) {
            const block = lines.slice(i, i + minBlockSize).join('\n');
            const remaining = lines.slice(i + minBlockSize).join('\n');
            if (remaining.includes(block) && block.trim().length > 100) {
                operations.push({
                    type: 'extract-function',
                    description: 'Duplicate code block detected',
                    file: filePath,
                    startLine: i + 1,
                    endLine: i + minBlockSize + 1,
                    newCode: '', // Would be generated during extraction
                    impact: 'medium',
                    confidence: 0.6,
                    dependencies: []
                });
            }
        }
        return operations;
    }
    // Helper methods
    calculateOperationScore(operation) {
        const impactScore = { low: 1, medium: 2, high: 3 }[operation.impact];
        return operation.confidence * impactScore;
    }
    estimateRefactoringTime(operations) {
        return operations.reduce((total, op) => {
            const timeMap = { low: 5, medium: 15, high: 30 };
            return total + timeMap[op.impact];
        }, 0);
    }
    assessRiskLevel(operations) {
        const highRiskOps = operations.filter(op => op.impact === 'high').length;
        const mediumRiskOps = operations.filter(op => op.impact === 'medium').length;
        if (highRiskOps > 3)
            return 'high';
        if (highRiskOps > 0 || mediumRiskOps > 5)
            return 'medium';
        return 'low';
    }
    identifyPrerequisites(operations) {
        const prerequisites = new Set();
        if (operations.some(op => op.type === 'extract-function')) {
            prerequisites.add('Ensure all tests pass before refactoring');
        }
        if (operations.some(op => op.impact === 'high')) {
            prerequisites.add('Create backup of current code');
            prerequisites.add('Review changes with team');
        }
        return Array.from(prerequisites);
    }
    identifyBenefits(operations) {
        const benefits = new Set();
        operations.forEach(op => {
            if (op.type === 'extract-function') {
                benefits.add('Improved code reusability');
                benefits.add('Better code organization');
            }
            if (op.type === 'optimize') {
                benefits.add('Better performance');
                benefits.add('Cleaner code');
            }
        });
        return Array.from(benefits);
    }
    groupOperationsByFile(operations) {
        const grouped = new Map();
        operations.forEach(op => {
            if (!grouped.has(op.file)) {
                grouped.set(op.file, []);
            }
            grouped.get(op.file).push(op);
        });
        return grouped;
    }
    async applyFileOperations(filePath, operations) {
        // Sort operations by line number (descending) to avoid line number conflicts
        operations.sort((a, b) => b.startLine - a.startLine);
        let content = await this.getFileContent(filePath);
        if (!content)
            throw new Error('File not found');
        for (const operation of operations) {
            if (operation.newCode) {
                content = operation.newCode;
            }
        }
        await fs.writeFile(path.resolve(this.workspaceRoot, filePath), content, 'utf-8');
    }
    generateRefactoringSummary(result) {
        return `Refactoring completed: ${result.operationsApplied} operations applied to ${result.filesModified.length} files. ${result.errors.length} errors, ${result.warnings.length} warnings.`;
    }
    async getAllProjectFiles() {
        // Implementation would use glob to find all relevant files
        return [];
    }
    async getFileContent(filePath) {
        try {
            const fullPath = path.resolve(this.workspaceRoot, filePath);
            return await fs.readFile(fullPath, 'utf-8');
        }
        catch {
            return null;
        }
    }
    analyzeExtractedCode(code) {
        // Simplified analysis - would be more sophisticated in practice
        return {
            parameters: [],
            returnValue: 'void'
        };
    }
    generateExtractedFunction(name, code, analysis) {
        return `function ${name}() {\n${code}\n}`;
    }
    generateFunctionCall(name, parameters) {
        return `${name}();`;
    }
    async findSymbolReferences(symbol) {
        // Would search for all references to the symbol
        return [];
    }
    replaceSymbolName(content, oldName, newName) {
        const regex = new RegExp(`\\b${oldName}\\b`, 'g');
        return content.replace(regex, newName);
    }
    optimizeFileImports(content) {
        // Simplified import optimization
        const lines = content.split('\n');
        const imports = lines.filter(line => line.startsWith('import'));
        const nonImports = lines.filter(line => !line.startsWith('import'));
        // Sort imports alphabetically
        imports.sort();
        return [...imports, '', ...nonImports].join('\n');
    }
}
exports.AutomatedRefactoring = AutomatedRefactoring;
//# sourceMappingURL=AutomatedRefactoring.js.map