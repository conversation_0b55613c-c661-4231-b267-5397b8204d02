"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Chatbot = void 0;
const react_1 = __importStar(require("react"));
const MicrochipAPI_1 = require("../services/MicrochipAPI");
require("../components/Chatbot.css");
const Chatbot = ({ onDisconnect, apiKey }) => {
    const [messages, setMessages] = (0, react_1.useState)([
        {
            id: '1',
            text: "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?",
            isBot: true,
            timestamp: new Date()
        }
    ]);
    const [inputMessage, setInputMessage] = (0, react_1.useState)('');
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)('');
    const messagesEndRef = (0, react_1.useRef)(null);
    const inputRef = (0, react_1.useRef)(null);
    // Set API key when component mounts
    (0, react_1.useEffect)(() => {
        if (apiKey) {
            MicrochipAPI_1.microchipAPI.setApiKey(apiKey);
        }
    }, [apiKey]);
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    (0, react_1.useEffect)(() => {
        scrollToBottom();
    }, [messages]);
    (0, react_1.useEffect)(() => {
        // Focus input on mount
        inputRef.current?.focus();
    }, []);
    const handleSendMessage = async (e) => {
        e.preventDefault();
        if (!inputMessage.trim() || isLoading) {
            return;
        }
        const userMessage = {
            id: Date.now().toString(),
            text: inputMessage.trim(),
            isBot: false,
            timestamp: new Date()
        };
        setMessages(prev => [...prev, userMessage]);
        setInputMessage('');
        setIsLoading(true);
        setError('');
        try {
            const response = await MicrochipAPI_1.microchipAPI.sendMessage(userMessage.text);
            const botMessage = {
                id: (Date.now() + 1).toString(),
                text: response,
                isBot: true,
                timestamp: new Date()
            };
            setMessages(prev => [...prev, botMessage]);
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An error occurred';
            setError(errorMessage);
            // Add error message to chat
            const errorChatMessage = {
                id: (Date.now() + 1).toString(),
                text: `Sorry, I encountered an error: ${errorMessage}`,
                isBot: true,
                timestamp: new Date()
            };
            setMessages(prev => [...prev, errorChatMessage]);
            // Show error via VS Code
            if (window.vscode) {
                window.vscode.postMessage({
                    type: 'showError',
                    message: `Chat error: ${errorMessage}`
                });
            }
        }
        finally {
            setIsLoading(false);
        }
    };
    const handleClearChat = () => {
        setMessages([
            {
                id: '1',
                text: "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?",
                isBot: true,
                timestamp: new Date()
            }
        ]);
        MicrochipAPI_1.microchipAPI.clearHistory();
        setError('');
    };
    const formatTime = (date) => {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };
    return (<div className="chatbot-container">
      <div className="chatbot-header">
        <div className="header-info">
          <h1>🤖 Microchip AI Chatbot</h1>
          <span className="status">Connected</span>
        </div>
        <div className="header-actions">
          <button onClick={handleClearChat} className="clear-button" title="Clear chat history">
            🗑️ Clear
          </button>
          <button onClick={onDisconnect} className="disconnect-button" title="Change API key">
            🔑 Change Key
          </button>
        </div>
      </div>

      <div className="messages-container">
        {messages.map((message) => (<div key={message.id} className={`message ${message.isBot ? 'bot-message' : 'user-message'}`}>
            <div className="message-content">
              <div className="message-text">
                {message.text}
              </div>
              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>))}
        
        {isLoading && (<div className="message bot-message">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>)}
        
        <div ref={messagesEndRef}/>
      </div>

      {error && (<div className="error-banner">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>)}

      <form onSubmit={handleSendMessage} className="input-form">
        <div className="input-container">
          <input ref={inputRef} type="text" value={inputMessage} onChange={(e) => setInputMessage(e.target.value)} placeholder="Type your message here..." disabled={isLoading} className="message-input"/>
          <button type="submit" disabled={!inputMessage.trim() || isLoading} className="send-button">
            {isLoading ? '⏳' : '📤'}
          </button>
        </div>
      </form>
    </div>);
};
exports.Chatbot = Chatbot;
//# sourceMappingURL=Chatbot.js.map