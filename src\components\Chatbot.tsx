import React, { useState, useRef, useEffect } from 'react';
import { browserMicrochipAPI, type ChatMessage } from '../services/BrowserMicrochipAPI';
import { AgentSettings } from './AgentSettings';
import { AgentConfigurationManager, type AgentConfiguration } from '../services/AgentConfiguration';
import './Chatbot.css';

interface ChatbotProps {
  onDisconnect: () => void;
}

export const Chatbot: React.FC<ChatbotProps> = ({ onDisconnect }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?",
      isBot: true,
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [agentMode, setAgentMode] = useState(false);
  const [advancedAgentMode, setAdvancedAgentMode] = useState(false);
  const [showAgentDetails, setShowAgentDetails] = useState(false);
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [configManager] = useState(() => new AgentConfigurationManager());
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Focus input on mount
    inputRef.current?.focus();
  }, []);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputMessage.trim() || isLoading) {
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setError('');

    try {
      const response = await browserMicrochipAPI.sendMessage(userMessage.text);

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.message,
        isBot: true,
        timestamp: new Date(),
        intent: response.intent,
        toolUsed: response.toolUsed,
        confidence: response.confidence,
        reasoning: response.reasoning,
        toolExecutionSteps: response.toolExecutionSteps
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      
      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Sorry, I encountered an error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = () => {
    setMessages([
      {
        id: '1',
        text: "Hello! I'm your Microchip AI assistant. I can help you with questions about microcontrollers, development tools, products, and more. What would you like to know?",
        isBot: true,
        timestamp: new Date()
      }
    ]);
    browserMicrochipAPI.clearHistory();
    setError('');
  };

  const handleAgentModeToggle = () => {
    const newAgentMode = !agentMode;
    setAgentMode(newAgentMode);
    browserMicrochipAPI.setAgentMode(newAgentMode);

    if (!newAgentMode) {
      setAdvancedAgentMode(false);
      browserMicrochipAPI.setAdvancedAgentMode(false);
    }
  };

  const handleAdvancedAgentModeToggle = () => {
    if (!openaiApiKey && !advancedAgentMode) {
      const key = prompt('Enter your OpenAI API key for advanced agent features:');
      if (key) {
        setOpenaiApiKey(key);
        browserMicrochipAPI.setOpenAIApiKey(key);
      } else {
        return;
      }
    }

    const newAdvancedMode = !advancedAgentMode;
    setAdvancedAgentMode(newAdvancedMode);
    browserMicrochipAPI.setAdvancedAgentMode(newAdvancedMode);

    if (newAdvancedMode) {
      setAgentMode(true);
      browserMicrochipAPI.setAgentMode(true);
    }
  };

  const handleIndexWorkspace = async () => {
    if (advancedAgentMode) {
      setIsLoading(true);
      try {
        await browserMicrochipAPI.indexWorkspace();
        alert('Workspace indexed successfully for semantic search!');
      } catch (error) {
        alert('Failed to index workspace: ' + (error instanceof Error ? error.message : 'Unknown error'));
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleConfigurationChange = (config: AgentConfiguration) => {
    // Update local state
    setAgentMode(config.agentMode);
    setAdvancedAgentMode(config.advancedAgentMode);
    setShowAgentDetails(config.showAgentDetails);
    setOpenaiApiKey(config.openaiApiKey || '');

    // Update API service
    browserMicrochipAPI.setAgentMode(config.agentMode);
    browserMicrochipAPI.setAdvancedAgentMode(config.advancedAgentMode);
    if (config.openaiApiKey) {
      browserMicrochipAPI.setOpenAIApiKey(config.openaiApiKey);
    }
  };

  // Initialize configuration on mount
  useEffect(() => {
    const config = configManager.getConfiguration();
    handleConfigurationChange(config);
  }, [configManager]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="chatbot-container">
      <div className="chatbot-header">
        <div className="header-info">
          <h1>🤖 Microchip AI Chatbot</h1>
          <span className="status">Connected</span>
          <div className="agent-status">
            {advancedAgentMode && <span className="advanced-agent-badge">🧠 Advanced Agent</span>}
            {agentMode && !advancedAgentMode && <span className="agent-badge">🤖 Agent Mode</span>}
          </div>
        </div>
        <div className="header-actions">
          <button
            onClick={handleAgentModeToggle}
            className={`agent-toggle ${agentMode ? 'active' : ''}`}
            title="Toggle basic agent mode"
          >
            🤖 {agentMode ? 'Agent ON' : 'Agent OFF'}
          </button>
          <button
            onClick={handleAdvancedAgentModeToggle}
            className={`advanced-agent-toggle ${advancedAgentMode ? 'active' : ''}`}
            title="Toggle advanced LangChain agent mode"
          >
            🧠 {advancedAgentMode ? 'Advanced ON' : 'Advanced OFF'}
          </button>
          {advancedAgentMode && (
            <button
              onClick={handleIndexWorkspace}
              className="index-button"
              title="Index workspace for semantic search"
              disabled={isLoading}
            >
              📚 Index
            </button>
          )}
          <button
            onClick={() => setShowAgentDetails(!showAgentDetails)}
            className="details-toggle"
            title="Show/hide agent details"
          >
            ℹ️ Details
          </button>
          <button
            onClick={() => setShowSettings(true)}
            className="settings-button"
            title="Agent settings and configuration"
          >
            ⚙️ Settings
          </button>
          <button
            onClick={handleClearChat}
            className="clear-button"
            title="Clear chat history"
          >
            🗑️ Clear
          </button>
          <button
            onClick={onDisconnect}
            className="disconnect-button"
            title="Change API key"
          >
            🔑 Change Key
          </button>
        </div>
      </div>

      <div className="messages-container">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message ${message.isBot ? 'bot-message' : 'user-message'}`}
          >
            <div className="message-content">
              <div className="message-text">
                {message.text}
              </div>
              {message.isBot && showAgentDetails && (message.intent || message.toolUsed) && (
                <div className="agent-details">
                  {message.intent && (
                    <div className="agent-detail">
                      <span className="detail-label">Intent:</span>
                      <span className="detail-value">{message.intent}</span>
                    </div>
                  )}
                  {message.toolUsed && (
                    <div className="agent-detail">
                      <span className="detail-label">Tool:</span>
                      <span className="detail-value">{message.toolUsed}</span>
                    </div>
                  )}
                  {message.confidence && (
                    <div className="agent-detail">
                      <span className="detail-label">Confidence:</span>
                      <span className="detail-value">{(message.confidence * 100).toFixed(1)}%</span>
                    </div>
                  )}
                  {message.reasoning && (
                    <div className="agent-detail">
                      <span className="detail-label">Reasoning:</span>
                      <span className="detail-value">{message.reasoning}</span>
                    </div>
                  )}
                  {message.toolExecutionSteps && message.toolExecutionSteps.length > 0 && (
                    <div className="agent-detail">
                      <span className="detail-label">Steps:</span>
                      <div className="execution-steps">
                        {message.toolExecutionSteps.map((step, index) => (
                          <div key={index} className="execution-step">
                            {index + 1}. {step}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="message-time">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message bot-message">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          {error}
          <button onClick={() => setError('')} className="close-error">×</button>
        </div>
      )}

      <form onSubmit={handleSendMessage} className="input-form">
        <div className="input-container">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Type your message here..."
            disabled={isLoading}
            className="message-input"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="send-button"
          >
            {isLoading ? '⏳' : '📤'}
          </button>
        </div>
      </form>

      <AgentSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onConfigurationChange={handleConfigurationChange}
      />
    </div>
  );
};
