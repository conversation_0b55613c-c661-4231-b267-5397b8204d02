{"version": 3, "file": "AdvancedCodeAnalysis.js", "sourceRoot": "", "sources": ["../../src/services/AdvancedCodeAnalysis.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,2CAA6B;AAC7B,+BAA4B;AAC5B,+CAAiC;AA6DjC,MAAa,oBAAoB;IAI/B,YAAY,aAAsB;QAF1B,kBAAa,GAAqB,IAAI,GAAG,EAAE,CAAC;QAGlD,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5G,CAAC;IAED,iCAAiC;IACjC,KAAK,CAAC,cAAc;QAMlB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACpF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACnE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,wBAAwB,EAAE,gBAAgB,CAAC,CAAC;QAEhH,OAAO;YACL,OAAO;YACP,wBAAwB;YACxB,gBAAgB;YAChB,eAAe;SAChB,CAAC;IACJ,CAAC;IAED,uCAAuC;IAC/B,KAAK,CAAC,oBAAoB,CAAC,KAAe;QAChD,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAC7B,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC9C,eAAe,IAAI,QAAQ,CAAC,UAAU,CAAC;YACvC,oBAAoB,IAAI,QAAQ,CAAC,eAAe,CAAC;YACjD,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACtD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAEpD,OAAO;YACL,UAAU,EAAE,eAAe,GAAG,KAAK,CAAC,MAAM;YAC1C,eAAe,EAAE,oBAAoB,GAAG,KAAK,CAAC,MAAM;YACpD,YAAY,EAAE,MAAM,IAAI,CAAC,qBAAqB,EAAE;YAChD,UAAU;YACV,YAAY;YACZ,WAAW;SACZ,CAAC;IACJ,CAAC;IAED,0BAA0B;IAClB,KAAK,CAAC,WAAW,CAAC,QAAgB;QAKxC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAChF,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnE,OAAO;YACL,UAAU;YACV,eAAe;YACf,UAAU;SACX,CAAC;IACJ,CAAC;IAED,kCAAkC;IAC1B,6BAA6B,CAAC,OAAe;QACnD,gEAAgE;QAChE,MAAM,QAAQ,GAAG;YACf,YAAY;YACZ,eAAe;YACf,aAAa;YACb,YAAY;YACZ,eAAe;YACf,KAAK;YACL,OAAO;YACP,KAAK;SACN,CAAC;QAEF,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,kBAAkB;QAEtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,OAAO,EAAE,CAAC;gBACZ,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,kCAAkC;IAC1B,6BAA6B,CAAC,OAAe,EAAE,UAAkB;QACvE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;QACxE,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAE7D,+CAA+C;QAC/C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAChC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CACxF,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;IACxC,CAAC;IAED,yCAAyC;IACjC,uBAAuB,CAAC,OAAe;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,IAAI,EAAE,CAAC;QACtE,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,IAAI,EAAE,CAAC;QAEtE,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;QAChD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC9C,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;QACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEtC,MAAM,UAAU,GAAG,eAAe,GAAG,cAAc,CAAC;QACpD,MAAM,MAAM,GAAG,cAAc,GAAG,aAAa,CAAC;QAE9C,OAAO,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,qBAAqB;IACb,gBAAgB,CAAC,QAAgB,EAAE,OAAe,EAAE,KAAe;QACzE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,wBAAwB;QACxB,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC9E,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC7D,IAAI,UAAU,GAAG,CAAC,CAAC;oBACnB,IAAI,cAAc,GAAG,CAAC,CAAC;oBAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACtC,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAClD,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAClD,cAAc,EAAE,CAAC;wBAEjB,IAAI,UAAU,KAAK,CAAC,IAAI,cAAc,GAAG,EAAE,EAAE,CAAC;4BAC5C,MAAM,CAAC,IAAI,CAAC;gCACV,IAAI,EAAE,YAAY;gCAClB,QAAQ,EAAE,QAAQ;gCAClB,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,GAAG,CAAC;gCACX,WAAW,EAAE,yBAAyB,cAAc,SAAS;gCAC7D,UAAU,EAAE,sEAAsE;6BACnF,CAAC,CAAC;4BACH,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,MAAM,kBAAkB,GAAG,CAAC,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjE,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,aAAa;oBACnB,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,GAAG,CAAC;oBACX,WAAW,EAAE,+BAA+B;oBAC5C,UAAU,EAAE,8CAA8C;iBAC3D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qCAAqC;IAC7B,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,WAAI,EAAC,0BAA0B,EAAE;gBAC5C,GAAG,EAAE,IAAI,CAAC,aAAa;gBACvB,MAAM,EAAE,CAAC,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC;gBAC9E,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,uBAAuB;IACf,KAAK,CAAC,mBAAmB;QAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QACtE,IAAI,QAAQ,GAAa,EAAE,CAAC;QAE5B,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YACvD,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC;gBACrB,GAAG,WAAW,CAAC,YAAY;gBAC3B,GAAG,WAAW,CAAC,eAAe;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,EAAE,EAAE,oDAAoD;YAClE,QAAQ;YACR,QAAQ,EAAE,EAAE,EAAE,iDAAiD;YAC/D,MAAM,EAAE,EAAE,EAAE,sCAAsC;YAClD,QAAQ,EAAE,EAAE,CAAC,wCAAwC;SACtD,CAAC;IACJ,CAAC;IAED,8BAA8B;IACtB,KAAK,CAAC,kBAAkB;QAC9B,OAAO;YACL,UAAU,EAAE,CAAC,EAAE,wCAAwC;YACvD,QAAQ,EAAE,CAAC,EAAE,2CAA2C;YACxD,WAAW,EAAE,CAAC,EAAE,0CAA0C;YAC1D,sBAAsB,EAAE;gBACtB,2CAA2C;gBAC3C,uCAAuC;gBACvC,uBAAuB;gBACvB,yCAAyC;aAC1C;SACF,CAAC;IACJ,CAAC;IAED,0BAA0B;IAClB,KAAK,CAAC,qBAAqB;QACjC,6DAA6D;QAC7D,OAAO,CAAC,CAAC,CAAC,cAAc;IAC1B,CAAC;IAED,qCAAqC;IAC7B,KAAK,CAAC,gCAAgC,CAAC,KAAe;QAC5D,MAAM,aAAa,GAA6B,EAAE,CAAC;QAEnD,2DAA2D;QAC3D,4CAA4C;QAE5C,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,4BAA4B;IACpB,KAAK,CAAC,uBAAuB,CAAC,KAAe;QACnD,MAAM,eAAe,GAA4B,EAAE,CAAC;QAEpD,6CAA6C;QAC7C,iCAAiC;QAEjC,OAAO;YACL,eAAe;YACf,SAAS,EAAE,CAAC;YACZ,eAAe,EAAE;gBACf,8BAA8B;gBAC9B,qCAAqC;gBACrC,0BAA0B;gBAC1B,mDAAmD;aACpD;SACF,CAAC;IACJ,CAAC;IAED,6CAA6C;IACrC,KAAK,CAAC,uBAAuB,CACnC,OAAoB,EACpB,WAAqC,EACrC,QAA0B;QAE1B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YAC5B,eAAe,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QACvG,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAC3B,eAAe,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AA1SD,oDA0SC"}